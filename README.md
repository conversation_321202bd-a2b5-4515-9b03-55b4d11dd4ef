# Remote Desktop App

A cross-platform remote desktop application built with Electron, designed to work seamlessly in restricted environments like Safe Exam Browser (SEB).

## Features

- **Cross-platform compatibility** - Works on Windows, macOS, and Linux
- **SEB-compatible** - Special handling for Safe Exam Browser environments
- **Real-time screen sharing** - High-quality video streaming with adjustable quality
- **Remote control** - Full mouse and keyboard control
- **P2P connection** - Direct peer-to-peer connection using WebRTC
- **Data channels** - Low-latency control commands
- **Modern UI** - Clean, intuitive interface similar to AnyDesk
- **Connection statistics** - Real-time latency, FPS, and bandwidth monitoring
- **Screenshot capture** - Take screenshots of remote sessions
- **Clipboard sync** - Synchronize clipboard between devices (optional)

## Architecture

### Core Components

1. **Electron Main Process** (`main.js`)
   - Window management
   - Native API access
   - Screen capture coordination

2. **Renderer Process** (`src/renderer/`)
   - User interface
   - WebRTC client management
   - Canvas-based screen display

3. **Signaling Server** (`src/server/`)
   - WebSocket-based signaling
   - User ID management
   - Connection coordination

4. **Screen Capture** (`src/capture/`)
   - Multi-method screen capture
   - SEB-compatible fallbacks
   - Quality optimization

5. **Network Layer** (`src/network/`)
   - WebRTC peer connections
   - Data channel management
   - Connection statistics

6. **Input Handling** (`src/input/`)
   - Mouse and keyboard simulation
   - Cross-platform input mapping
   - Clipboard operations

## Installation

### Prerequisites

- Node.js 16+ 
- npm or yarn
- Python 3.x (for native modules)
- Visual Studio Build Tools (Windows)
- Xcode Command Line Tools (macOS)

### Setup

1. **Clone and install dependencies:**
```bash
git clone <repository-url>
cd remote-desktop-app
npm install
```

2. **Install native dependencies:**
```bash
# Windows
npm install --global windows-build-tools

# macOS
xcode-select --install

# Linux (Ubuntu/Debian)
sudo apt-get install build-essential libxtst6 libpng++-dev
```

3. **Start the application:**
```bash
# Development mode
npm run dev

# Production mode
npm start
```

## Usage

### Starting a Session

1. **Share your screen:**
   - Click "Start Sharing" or use the welcome screen
   - Your unique ID will be displayed
   - Share this ID with the person who wants to connect

2. **Connect to a remote computer:**
   - Enter the remote user's ID
   - Click "Connect"
   - Wait for the connection to establish

### Controls

- **Mouse control:** Click and drag on the remote screen
- **Keyboard input:** Click on the remote screen and type
- **Scroll:** Use mouse wheel over the remote screen
- **Screenshot:** Click the camera icon
- **Fullscreen:** Click the expand icon
- **Disconnect:** Click the X icon

### Settings

- **Quality:** Adjust video quality (High/Medium/Low)
- **Audio:** Enable/disable audio transmission
- **Clipboard Sync:** Synchronize clipboard content

## SEB Compatibility

The application includes special handling for Safe Exam Browser environments:

### Detection
- Automatic SEB environment detection
- User agent analysis
- Protocol checking

### Fallback Methods
- Canvas-based screen capture
- Alternative WebRTC configurations
- Reduced security restrictions

### Configuration
```javascript
// Enable SEB compatibility mode
const sebCompatible = true;

// Adjust capture method
const captureMethod = sebCompatible ? 'canvas' : 'native';
```

## Development

### Project Structure
```
remote-desktop-app/
├── main.js                 # Electron main process
├── package.json            # Dependencies and scripts
├── src/
│   ├── renderer/           # Frontend (HTML, CSS, JS)
│   │   ├── index.html
│   │   ├── style.css
│   │   └── app.js
│   ├── server/             # Signaling server
│   │   └── signaling-server.js
│   ├── capture/            # Screen capture logic
│   │   └── screen-capture.js
│   ├── network/            # WebRTC networking
│   │   └── webrtc-client.js
│   └── input/              # Input handling
│       └── input-handler.js
└── README.md
```

### Building

```bash
# Build for current platform
npm run build

# Build for all platforms
npm run build:all

# Build for specific platform
npm run build:win
npm run build:mac
npm run build:linux
```

### Testing

```bash
# Run tests
npm test

# Run with coverage
npm run test:coverage

# Run specific test
npm test -- --grep "WebRTC"
```

## Configuration

### Environment Variables

```bash
# Signaling server port
PORT=3001

# Development mode
NODE_ENV=development

# Enable debug logging
DEBUG=true

# SEB compatibility mode
SEB_MODE=true
```

### Application Settings

```javascript
// Quality presets
const qualitySettings = {
  high: { width: 1920, height: 1080, fps: 30 },
  medium: { width: 1280, height: 720, fps: 24 },
  low: { width: 854, height: 480, fps: 15 }
};

// WebRTC configuration
const rtcConfig = {
  iceServers: [
    { urls: 'stun:stun.l.google.com:19302' }
  ]
};
```

## Troubleshooting

### Common Issues

1. **Screen capture not working:**
   - Check permissions in system settings
   - Try running as administrator (Windows)
   - Enable SEB compatibility mode

2. **Connection fails:**
   - Check firewall settings
   - Verify signaling server is running
   - Try different STUN servers

3. **Poor performance:**
   - Lower video quality
   - Reduce frame rate
   - Check network bandwidth

4. **Input not working:**
   - Ensure robotjs is properly installed
   - Check accessibility permissions (macOS)
   - Verify input handler is enabled

### Debug Mode

```bash
# Enable debug logging
DEBUG=* npm start

# Electron debug
npm start -- --enable-logging --log-level=0
```

## Security Considerations

- **P2P encryption:** All WebRTC connections are encrypted
- **No data storage:** No session data is stored on servers
- **Permission-based:** Requires explicit user permission for screen access
- **Firewall friendly:** Uses STUN/TURN for NAT traversal

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

For issues and questions:
- Create an issue on GitHub
- Check the troubleshooting section
- Review the documentation

## Roadmap

- [ ] File transfer support
- [ ] Multi-monitor support
- [ ] Mobile app companion
- [ ] Recording functionality
- [ ] Chat integration
- [ ] Session management
- [ ] Enterprise features
