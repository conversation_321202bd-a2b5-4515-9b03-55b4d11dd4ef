/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #1e1e1e;
    color: #ffffff;
    overflow: hidden;
    user-select: none;
}

#app {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.app-header {
    background: #2d2d2d;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    border-bottom: 1px solid #404040;
    -webkit-app-region: drag;
}

.header-left, .header-center, .header-right {
    display: flex;
    align-items: center;
    -webkit-app-region: no-drag;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #00a8ff;
}

.logo i {
    font-size: 16px;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
}

.status-indicator {
    font-size: 8px;
    color: #ff4757;
}

.status-indicator.connected {
    color: #2ed573;
}

.status-indicator.warning {
    color: #ffa502;
}

.header-btn {
    background: none;
    border: none;
    color: #ffffff;
    padding: 8px 12px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.header-btn:hover {
    background: #404040;
}

.close-btn:hover {
    background: #ff4757;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

/* Sidebar */
.sidebar {
    width: 300px;
    background: #252525;
    border-right: 1px solid #404040;
    padding: 20px;
    overflow-y: auto;
}

.sidebar-section {
    margin-bottom: 30px;
}

.sidebar-section h3 {
    font-size: 14px;
    margin-bottom: 15px;
    color: #00a8ff;
    display: flex;
    align-items: center;
    gap: 8px;
}

.connection-id-section label,
.connect-section label,
.setting-item label {
    display: block;
    margin-bottom: 8px;
    font-size: 12px;
    color: #cccccc;
}

.id-display {
    display: flex;
    gap: 5px;
    margin-bottom: 15px;
}

.id-display input {
    flex: 1;
    background: #1e1e1e;
    border: 1px solid #404040;
    color: #ffffff;
    padding: 8px;
    border-radius: 4px;
    font-size: 12px;
}

.id-display button {
    background: #404040;
    border: none;
    color: #ffffff;
    padding: 8px 10px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.id-display button:hover {
    background: #505050;
}

.connect-section input {
    width: 100%;
    background: #1e1e1e;
    border: 1px solid #404040;
    color: #ffffff;
    padding: 8px;
    border-radius: 4px;
    margin-bottom: 15px;
    font-size: 12px;
}

.primary-btn {
    width: 100%;
    background: #00a8ff;
    border: none;
    color: #ffffff;
    padding: 10px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: background-color 0.2s;
}

.primary-btn:hover {
    background: #0097e6;
}

.primary-btn:disabled {
    background: #404040;
    cursor: not-allowed;
}

.setting-item {
    margin-bottom: 15px;
}

.setting-item label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.setting-item input[type="checkbox"] {
    accent-color: #00a8ff;
}

.setting-item select {
    width: 100%;
    background: #1e1e1e;
    border: 1px solid #404040;
    color: #ffffff;
    padding: 6px;
    border-radius: 4px;
    margin-top: 5px;
}

.info-section {
    font-size: 12px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    color: #cccccc;
}

/* Main View */
.main-view {
    flex: 1;
    background: #1a1a1a;
    position: relative;
}

.view-container {
    width: 100%;
    height: 100%;
    position: relative;
}

/* Welcome Screen */
.welcome-screen {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
}

.welcome-content {
    max-width: 400px;
}

.welcome-icon {
    font-size: 64px;
    color: #00a8ff;
    margin-bottom: 20px;
}

.welcome-content h2 {
    margin-bottom: 10px;
    font-size: 24px;
}

.welcome-content p {
    color: #cccccc;
    margin-bottom: 30px;
}

.welcome-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.welcome-btn {
    background: #2d2d2d;
    border: 2px solid #404040;
    color: #ffffff;
    padding: 15px 20px;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    transition: all 0.2s;
    min-width: 120px;
}

.welcome-btn:hover {
    border-color: #00a8ff;
    background: #333333;
}

.welcome-btn i {
    font-size: 20px;
}

/* Remote Screen */
.remote-screen,
.local-preview {
    width: 100%;
    height: 100%;
    position: relative;
}

.screen-container,
.preview-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #000000;
}

#remoteCanvas,
#localCanvas {
    max-width: 100%;
    max-height: 100%;
    border: 1px solid #404040;
    cursor: crosshair;
}

.screen-overlay,
.preview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.overlay-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    gap: 5px;
    pointer-events: auto;
}

.overlay-controls button {
    background: rgba(0, 0, 0, 0.7);
    border: none;
    color: #ffffff;
    padding: 8px 10px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.overlay-controls button:hover {
    background: rgba(0, 0, 0, 0.9);
}

.overlay-info {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    padding: 10px 20px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    pointer-events: auto;
}

.stop-btn {
    background: #ff4757;
    border: none;
    color: #ffffff;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.stop-btn:hover {
    background: #ff3838;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-content {
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #404040;
    border-top: 4px solid #00a8ff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 60px;
    right: 20px;
    z-index: 1001;
}

.toast {
    background: #2d2d2d;
    border: 1px solid #404040;
    border-left: 4px solid #00a8ff;
    color: #ffffff;
    padding: 12px 16px;
    border-radius: 4px;
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    animation: slideIn 0.3s ease-out;
    max-width: 300px;
}

.toast.error {
    border-left-color: #ff4757;
}

.toast.success {
    border-left-color: #2ed573;
}

.toast.warning {
    border-left-color: #ffa502;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Scrollbar Styles */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #1e1e1e;
}

::-webkit-scrollbar-thumb {
    background: #404040;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #505050;
}
