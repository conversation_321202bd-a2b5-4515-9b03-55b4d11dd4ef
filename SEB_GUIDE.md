# 🛡️ Safe Exam Browser (SEB) Integration Guide

## 📋 Overview
This guide provides comprehensive instructions for using the Remote Desktop App within Safe Exam Browser (SEB) environments, including setup, configuration, and troubleshooting.

---

## 🎯 SEB Compatibility Features

### ✅ Automatic Detection
- **Smart Detection**: Automatically detects SEB environment
- **User Agent Analysis**: Recognizes SEB-specific browser signatures
- **API Testing**: Tests for restricted APIs and adjusts accordingly
- **Performance Optimization**: Automatically reduces resource usage

### ✅ Fallback Mechanisms
- **Canvas Screen Capture**: When native screen APIs are blocked
- **LocalStorage Clipboard**: When clipboard API is restricted
- **Reduced Quality**: Optimized video settings for SEB performance
- **Alternative Network**: SEB-compatible WebRTC configuration

---

## 🔧 SEB Configuration

### 1. Basic SEB Settings
```json
{
  "allowedDisplays": 1,
  "allowQuit": false,
  "allowReload": false,
  "kiosk": true,
  "enableRightMouse": true,
  "showMenuBar": false,
  "showTaskBar": false,
  "hideBrowserWindowToolbar": true
}
```

### 2. Network Configuration
```json
{
  "urlFilterEnable": true,
  "allowedURLs": [
    "https://remote-desktop-seb.herokuapp.com/*",
    "http://localhost:*",
    "https://cdnjs.cloudflare.com/*"
  ],
  "newBrowserWindowByLinkPolicy": 2,
  "newBrowserWindowByScriptPolicy": 2
}
```

### 3. Security Settings
```json
{
  "enableF12": false,
  "enableCtrlAltDel": false,
  "enableAltTab": false,
  "enablePrintScreen": false,
  "monitorProcesses": false,
  "killExplorerShell": false
}
```

---

## 🚀 Installation & Setup

### Method 1: Direct Installation
1. **Download** the Remote Desktop App installer
2. **Install** the application normally
3. **Configure SEB** to allow the application
4. **Launch** through SEB

### Method 2: Portable Version
1. **Download** the portable version
2. **Extract** to a folder accessible by SEB
3. **Configure SEB** to start the application
4. **Set startup URL** to the app's index.html

### Method 3: SEB Integration
```json
{
  "startURL": "file:///C:/RemoteDesktop/src/renderer/index.html",
  "allowedExecutables": [
    {
      "active": true,
      "allowUser": false,
      "autostart": true,
      "executable": "C:\\RemoteDesktop\\RemoteDesktop.exe",
      "iconInTaskbar": false,
      "runInBackground": false,
      "title": "Remote Desktop"
    }
  ]
}
```

---

## 🔍 SEB Detection & Behavior

### Automatic Adaptations
When SEB is detected, the app automatically:

1. **Reduces Video Quality**
   - Resolution: 1280x720 (instead of 1920x1080)
   - Frame Rate: 15fps (instead of 30fps)
   - Bitrate: 1Mbps (instead of 5Mbps)

2. **Enables Fallback Methods**
   - Canvas-based screen capture
   - LocalStorage clipboard sync
   - Simplified UI elements

3. **Shows User Notifications**
   - SEB mode indicator
   - Performance warnings
   - Feature limitations

### Manual SEB Mode
Force SEB mode for testing:
```javascript
// In browser console
localStorage.setItem('forceSEBMode', 'true');
location.reload();
```

---

## 🎮 Usage in SEB Environment

### 1. Starting a Session
1. **Launch** the Remote Desktop App in SEB
2. **Wait** for SEB detection (automatic)
3. **Click** "Start Sharing" button
4. **Allow** screen capture permissions
5. **Share** the generated ID

### 2. Connecting to Remote
1. **Receive** ID from the sharing user
2. **Enter** ID in the "Remote ID" field
3. **Click** "Connect" button
4. **Wait** for connection establishment

### 3. Remote Control
- **Mouse Control**: Click and drag on remote screen
- **Keyboard Input**: Click on remote screen first, then type
- **Scroll**: Use mouse wheel over remote screen
- **Quality**: Automatically optimized for SEB

---

## ⚠️ Known Limitations in SEB

### 1. Screen Capture Restrictions
- **Native APIs Blocked**: SEB may block `getDisplayMedia`
- **Fallback Used**: Canvas capture with simulated content
- **Performance Impact**: Reduced frame rate and quality

### 2. Clipboard Limitations
- **API Restricted**: Clipboard API may be blocked
- **LocalStorage Fallback**: Uses browser storage instead
- **Manual Copy**: May require manual copy/paste

### 3. Network Restrictions
- **Proxy Filtering**: SEB may filter network requests
- **STUN/TURN Issues**: Some ICE servers may be blocked
- **Timeout Increases**: Longer connection times

### 4. UI Limitations
- **Fullscreen Restricted**: May not work in SEB kiosk mode
- **Right-click Disabled**: Context menus may be blocked
- **Keyboard Shortcuts**: Some shortcuts disabled by SEB

---

## 🔧 Troubleshooting

### Connection Issues

#### Problem: Cannot connect to server
**Solutions:**
1. Check SEB allowed URLs configuration
2. Verify internet connectivity
3. Try alternative server URLs
4. Check firewall settings

#### Problem: Connection drops frequently
**Solutions:**
1. Increase connection timeout in SEB
2. Check network stability
3. Reduce video quality
4. Use wired connection instead of WiFi

### Screen Capture Issues

#### Problem: Black screen or no video
**Solutions:**
1. Grant screen recording permissions
2. Check SEB screen capture settings
3. Try canvas fallback mode
4. Restart the application

#### Problem: Poor video quality
**Solutions:**
1. This is normal in SEB mode
2. Quality is automatically reduced for performance
3. Check network bandwidth
4. Close unnecessary applications

### Performance Issues

#### Problem: Slow or laggy connection
**Solutions:**
1. Reduce video quality to "Low"
2. Close other browser tabs
3. Check system resources
4. Use ethernet connection

#### Problem: High CPU usage
**Solutions:**
1. Lower frame rate to 10fps
2. Reduce screen resolution
3. Enable hardware acceleration if allowed
4. Close background applications

---

## 🧪 Testing SEB Compatibility

### 1. Pre-deployment Testing
```bash
# Test SEB detection
npm run test-seb

# Test with SEB simulator
npm run test-seb-simulator

# Test network restrictions
npm run test-network
```

### 2. Manual Testing Checklist
- [ ] App launches in SEB environment
- [ ] SEB detection works correctly
- [ ] Screen capture functions (with fallback)
- [ ] Remote control works
- [ ] Connection is stable
- [ ] Performance is acceptable
- [ ] Error handling works
- [ ] User notifications appear

### 3. Performance Benchmarks
- **Connection Time**: < 30 seconds
- **Video Latency**: < 500ms
- **Frame Rate**: 10-15fps
- **CPU Usage**: < 50%
- **Memory Usage**: < 200MB

---

## 📊 SEB Configuration Examples

### Example 1: Basic Remote Desktop
```json
{
  "startURL": "file:///C:/RemoteDesktop/index.html",
  "allowedURLs": [
    "https://remote-desktop-seb.herokuapp.com/*"
  ],
  "kiosk": true,
  "allowQuit": false,
  "enableRightMouse": true
}
```

### Example 2: Exam Environment
```json
{
  "startURL": "https://remote-desktop-seb.herokuapp.com",
  "allowedURLs": [
    "https://remote-desktop-seb.herokuapp.com/*",
    "https://cdnjs.cloudflare.com/*"
  ],
  "kiosk": true,
  "allowQuit": false,
  "enableRightMouse": false,
  "monitorProcesses": true,
  "killExplorerShell": true
}
```

### Example 3: Development Testing
```json
{
  "startURL": "http://localhost:3000",
  "allowedURLs": [
    "http://localhost:*",
    "https://remote-desktop-seb.herokuapp.com/*"
  ],
  "kiosk": false,
  "allowQuit": true,
  "enableF12": true
}
```

---

## 🆘 Support & Resources

### Documentation
- **Main README**: Complete application documentation
- **API Reference**: Technical implementation details
- **Deployment Guide**: Server setup instructions

### Support Channels
- **GitHub Issues**: Bug reports and feature requests
- **Email Support**: <EMAIL>
- **Documentation**: https://remote-desktop-seb.herokuapp.com/docs

### Community
- **Discord**: Join our developer community
- **Forums**: User discussions and tips
- **Blog**: Updates and tutorials

---

## 🔄 Updates & Maintenance

### Automatic Updates
- **Client Updates**: Automatic update checking
- **Server Updates**: Zero-downtime deployments
- **SEB Compatibility**: Continuous compatibility testing

### Manual Updates
1. **Download** latest version
2. **Replace** existing installation
3. **Update** SEB configuration if needed
4. **Test** functionality

### Version Compatibility
- **Client-Server**: Backward compatible
- **SEB Versions**: Tested with SEB 2.x and 3.x
- **Browser Support**: Chrome, Firefox, Safari, Edge

---

**🎉 You're now ready to use Remote Desktop App in SEB environments!**

For additional support or questions, please refer to our documentation or contact support.
