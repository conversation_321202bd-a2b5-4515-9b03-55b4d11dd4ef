const { ipc<PERSON><PERSON><PERSON> } = require('electron');
const io = require('socket.io-client');

class RemoteDesktopApp {
    constructor() {
        this.socket = null;
        this.localStream = null;
        this.remoteStream = null;
        this.peerConnection = null;
        this.dataChannel = null;
        this.isSharing = false;
        this.isConnected = false;
        this.myId = null;
        this.remoteId = null;
        this.canvas = null;
        this.ctx = null;
        this.localCanvas = null;
        this.localCtx = null;

        this.init();
    }

    async init() {
        this.setupUI();
        this.setupSocketConnection();
        this.setupWebRTC();
        await this.generateId();
    }

    setupUI() {
        // Header controls
        document.getElementById('minimizeBtn').addEventListener('click', () => {
            ipcRenderer.invoke('minimize-window');
        });

        document.getElementById('maximizeBtn').addEventListener('click', () => {
            ipcRenderer.invoke('maximize-window');
        });

        document.getElementById('closeBtn').addEventListener('click', () => {
            ipcRenderer.invoke('close-window');
        });

        // Main controls
        document.getElementById('startSharingBtn').addEventListener('click', () => {
            this.startSharing();
        });

        document.getElementById('connectBtn').addEventListener('click', () => {
            const remoteId = document.getElementById('remoteId').value.trim();
            if (remoteId) {
                this.connectToRemote(remoteId);
            }
        });

        document.getElementById('copyIdBtn').addEventListener('click', () => {
            this.copyIdToClipboard();
        });

        document.getElementById('welcomeShareBtn').addEventListener('click', () => {
            this.startSharing();
        });

        document.getElementById('welcomeConnectBtn').addEventListener('click', () => {
            document.getElementById('remoteId').focus();
        });

        document.getElementById('stopSharingBtn').addEventListener('click', () => {
            this.stopSharing();
        });

        document.getElementById('disconnectBtn').addEventListener('click', () => {
            this.disconnect();
        });

        document.getElementById('fullscreenBtn').addEventListener('click', () => {
            this.toggleFullscreen();
        });

        document.getElementById('screenshotBtn').addEventListener('click', () => {
            this.takeScreenshot();
        });

        // Canvas setup
        this.canvas = document.getElementById('remoteCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.localCanvas = document.getElementById('localCanvas');
        this.localCtx = this.localCanvas.getContext('2d');

        // Mouse and keyboard events for remote control
        this.setupRemoteControl();
    }

    setupSocketConnection() {
        this.socket = io('http://localhost:3002');

        this.socket.on('connect', () => {
            console.log('Connected to signaling server');
            this.updateConnectionStatus('Connected to server', 'connected');
        });

        this.socket.on('disconnect', () => {
            console.log('Disconnected from signaling server');
            this.updateConnectionStatus('Disconnected', 'disconnected');
        });

        this.socket.on('id-assigned', (id) => {
            this.myId = id;
            document.getElementById('myId').value = id;
            this.showToast(`Your ID: ${id}`, 'success');
        });

        this.socket.on('offer', async (data) => {
            await this.handleOffer(data);
        });

        this.socket.on('answer', async (data) => {
            await this.handleAnswer(data);
        });

        this.socket.on('ice-candidate', async (data) => {
            await this.handleIceCandidate(data);
        });

        this.socket.on('user-disconnected', () => {
            this.handleRemoteDisconnect();
        });

        this.socket.on('error', (error) => {
            this.showToast(`Error: ${error}`, 'error');
        });
    }

    setupWebRTC() {
        const configuration = {
            iceServers: [
                { urls: 'stun:stun.l.google.com:19302' },
                { urls: 'stun:stun1.l.google.com:19302' }
            ]
        };

        this.peerConnection = new RTCPeerConnection(configuration);

        this.peerConnection.onicecandidate = (event) => {
            if (event.candidate) {
                this.socket.emit('ice-candidate', {
                    to: this.remoteId,
                    candidate: event.candidate
                });
            }
        };

        this.peerConnection.ontrack = (event) => {
            this.remoteStream = event.streams[0];
            this.displayRemoteStream();
        };

        this.peerConnection.ondatachannel = (event) => {
            const channel = event.channel;
            this.setupDataChannel(channel);
        };

        this.peerConnection.onconnectionstatechange = () => {
            console.log('Connection state:', this.peerConnection.connectionState);
            if (this.peerConnection.connectionState === 'connected') {
                this.isConnected = true;
                this.updateConnectionStatus('Connected', 'connected');
                this.hideLoading();
            } else if (this.peerConnection.connectionState === 'disconnected') {
                this.handleRemoteDisconnect();
            }
        };
    }

    async generateId() {
        // Generate a unique 9-digit ID
        this.myId = Math.random().toString(36).substr(2, 9).toUpperCase();
        document.getElementById('myId').value = this.myId;
    }

    async startSharing() {
        try {
            this.showLoading('Starting screen share...');

            // Get screen sources
            const sources = await ipcRenderer.invoke('get-sources');

            if (sources.length === 0) {
                throw new Error('No screen sources available');
            }

            // Use the first screen source (primary display)
            const screenSource = sources.find(source => source.name === 'Entire Screen') || sources[0];

            // Get screen stream with SEB-compatible settings
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    mandatory: {
                        chromeMediaSource: 'desktop'
                    }
                },
                video: {
                    mandatory: {
                        chromeMediaSource: 'desktop',
                        chromeMediaSourceId: screenSource.id,
                        maxWidth: 1920,
                        maxHeight: 1080,
                        maxFrameRate: 30
                    }
                }
            });

            this.localStream = stream;
            this.isSharing = true;

            // Add stream to peer connection
            stream.getTracks().forEach(track => {
                this.peerConnection.addTrack(track, stream);
            });

            // Show local preview
            this.showLocalPreview();
            this.displayLocalStream();

            // Update UI
            document.getElementById('startSharingBtn').disabled = true;
            document.getElementById('startSharingBtn').innerHTML = '<i class="fas fa-check"></i> Sharing';

            this.hideLoading();
            this.showToast('Screen sharing started', 'success');

        } catch (error) {
            console.error('Error starting screen share:', error);
            this.hideLoading();
            this.showToast(`Failed to start screen sharing: ${error.message}`, 'error');
        }
    }

    async connectToRemote(remoteId) {
        try {
            this.showLoading('Connecting to remote...');
            this.remoteId = remoteId;

            // Create data channel for mouse/keyboard events
            this.dataChannel = this.peerConnection.createDataChannel('control', {
                ordered: true
            });
            this.setupDataChannel(this.dataChannel);

            // Create offer
            const offer = await this.peerConnection.createOffer();
            await this.peerConnection.setLocalDescription(offer);

            // Send offer to remote peer
            this.socket.emit('offer', {
                to: remoteId,
                from: this.myId,
                offer: offer
            });

        } catch (error) {
            console.error('Error connecting to remote:', error);
            this.hideLoading();
            this.showToast(`Failed to connect: ${error.message}`, 'error');
        }
    }

    async handleOffer(data) {
        try {
            this.remoteId = data.from;

            await this.peerConnection.setRemoteDescription(data.offer);

            const answer = await this.peerConnection.createAnswer();
            await this.peerConnection.setLocalDescription(answer);

            this.socket.emit('answer', {
                to: data.from,
                from: this.myId,
                answer: answer
            });

        } catch (error) {
            console.error('Error handling offer:', error);
            this.showToast(`Failed to handle connection: ${error.message}`, 'error');
        }
    }

    async handleAnswer(data) {
        try {
            await this.peerConnection.setRemoteDescription(data.answer);
        } catch (error) {
            console.error('Error handling answer:', error);
        }
    }

    async handleIceCandidate(data) {
        try {
            await this.peerConnection.addIceCandidate(data.candidate);
        } catch (error) {
            console.error('Error handling ICE candidate:', error);
        }
    }

    setupDataChannel(channel) {
        channel.onopen = () => {
            console.log('Data channel opened');
        };

        channel.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleRemoteInput(data);
        };

        channel.onclose = () => {
            console.log('Data channel closed');
        };
    }

    setupRemoteControl() {
        // Mouse events
        this.canvas.addEventListener('mousedown', (e) => {
            this.sendInputEvent('mousedown', this.getMousePosition(e));
        });

        this.canvas.addEventListener('mouseup', (e) => {
            this.sendInputEvent('mouseup', this.getMousePosition(e));
        });

        this.canvas.addEventListener('mousemove', (e) => {
            this.sendInputEvent('mousemove', this.getMousePosition(e));
        });

        this.canvas.addEventListener('wheel', (e) => {
            e.preventDefault();
            this.sendInputEvent('wheel', {
                ...this.getMousePosition(e),
                deltaX: e.deltaX,
                deltaY: e.deltaY
            });
        });

        // Keyboard events
        document.addEventListener('keydown', (e) => {
            if (this.isConnected && document.activeElement === this.canvas) {
                e.preventDefault();
                this.sendInputEvent('keydown', {
                    key: e.key,
                    code: e.code,
                    ctrlKey: e.ctrlKey,
                    shiftKey: e.shiftKey,
                    altKey: e.altKey,
                    metaKey: e.metaKey
                });
            }
        });

        document.addEventListener('keyup', (e) => {
            if (this.isConnected && document.activeElement === this.canvas) {
                e.preventDefault();
                this.sendInputEvent('keyup', {
                    key: e.key,
                    code: e.code,
                    ctrlKey: e.ctrlKey,
                    shiftKey: e.shiftKey,
                    altKey: e.altKey,
                    metaKey: e.metaKey
                });
            }
        });
    }

    getMousePosition(e) {
        const rect = this.canvas.getBoundingClientRect();
        const scaleX = this.canvas.width / rect.width;
        const scaleY = this.canvas.height / rect.height;

        return {
            x: (e.clientX - rect.left) * scaleX,
            y: (e.clientY - rect.top) * scaleY
        };
    }

    sendInputEvent(type, data) {
        if (this.dataChannel && this.dataChannel.readyState === 'open') {
            this.dataChannel.send(JSON.stringify({
                type: type,
                data: data,
                timestamp: Date.now()
            }));
        }
    }

    handleRemoteInput(inputData) {
        // This would be handled by the input handler module
        // For now, just log the input
        console.log('Remote input:', inputData);
    }

    displayRemoteStream() {
        if (this.remoteStream) {
            const video = document.createElement('video');
            video.srcObject = this.remoteStream;
            video.autoplay = true;
            video.muted = true;

            video.onloadedmetadata = () => {
                this.canvas.width = video.videoWidth;
                this.canvas.height = video.videoHeight;
                this.drawVideoFrame(video);
            };

            this.showRemoteScreen();
        }
    }

    drawVideoFrame(video) {
        const draw = () => {
            if (video.readyState >= 2) {
                this.ctx.drawImage(video, 0, 0, this.canvas.width, this.canvas.height);
            }
            if (this.isConnected) {
                requestAnimationFrame(draw);
            }
        };
        draw();
    }

    displayLocalStream() {
        if (this.localStream) {
            const video = document.createElement('video');
            video.srcObject = this.localStream;
            video.autoplay = true;
            video.muted = true;

            video.onloadedmetadata = () => {
                this.localCanvas.width = video.videoWidth;
                this.localCanvas.height = video.videoHeight;
                this.drawLocalVideoFrame(video);
            };
        }
    }

    drawLocalVideoFrame(video) {
        const draw = () => {
            if (video.readyState >= 2) {
                this.localCtx.drawImage(video, 0, 0, this.localCanvas.width, this.localCanvas.height);
            }
            if (this.isSharing) {
                requestAnimationFrame(draw);
            }
        };
        draw();
    }

    showWelcomeScreen() {
        document.getElementById('welcomeScreen').style.display = 'flex';
        document.getElementById('remoteScreen').style.display = 'none';
        document.getElementById('localPreview').style.display = 'none';
    }

    showRemoteScreen() {
        document.getElementById('welcomeScreen').style.display = 'none';
        document.getElementById('remoteScreen').style.display = 'block';
        document.getElementById('localPreview').style.display = 'none';
    }

    showLocalPreview() {
        document.getElementById('welcomeScreen').style.display = 'none';
        document.getElementById('remoteScreen').style.display = 'none';
        document.getElementById('localPreview').style.display = 'block';
    }

    stopSharing() {
        if (this.localStream) {
            this.localStream.getTracks().forEach(track => track.stop());
            this.localStream = null;
        }

        this.isSharing = false;
        document.getElementById('startSharingBtn').disabled = false;
        document.getElementById('startSharingBtn').innerHTML = '<i class="fas fa-play"></i> Start Sharing';

        this.showWelcomeScreen();
        this.showToast('Screen sharing stopped', 'info');
    }

    disconnect() {
        if (this.peerConnection) {
            this.peerConnection.close();
        }

        if (this.localStream) {
            this.localStream.getTracks().forEach(track => track.stop());
        }

        this.isConnected = false;
        this.isSharing = false;
        this.remoteId = null;

        this.showWelcomeScreen();
        this.updateConnectionStatus('Disconnected', 'disconnected');
        this.showToast('Disconnected', 'info');
    }

    handleRemoteDisconnect() {
        this.disconnect();
        this.showToast('Remote user disconnected', 'warning');
    }

    copyIdToClipboard() {
        const idInput = document.getElementById('myId');
        idInput.select();
        document.execCommand('copy');
        this.showToast('ID copied to clipboard', 'success');
    }

    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.getElementById('remoteScreen').requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }

    takeScreenshot() {
        const link = document.createElement('a');
        link.download = `screenshot-${Date.now()}.png`;
        link.href = this.canvas.toDataURL();
        link.click();
        this.showToast('Screenshot saved', 'success');
    }

    updateConnectionStatus(text, status) {
        const statusElement = document.getElementById('connectionStatus');
        const indicator = statusElement.querySelector('.status-indicator');
        const textElement = statusElement.querySelector('span');

        textElement.textContent = text;
        indicator.className = `fas fa-circle status-indicator ${status}`;

        document.getElementById('statusText').textContent = text;
    }

    showLoading(text) {
        document.getElementById('loadingText').textContent = text;
        document.getElementById('loadingOverlay').style.display = 'flex';
    }

    hideLoading() {
        document.getElementById('loadingOverlay').style.display = 'none';
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;

        document.getElementById('toastContainer').appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 5000);
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new RemoteDesktopApp();
});
