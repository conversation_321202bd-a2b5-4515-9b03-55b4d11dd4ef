# 🚀 Quick Start Guide - Remote Desktop App

## Tổng quan
Ứng dụng Remote Desktop này được thiết kế đặc biệt để hoạt động tốt trong môi trường Safe Exam Browser (SEB) và các môi trường bị hạn chế khác.

## ✨ Tính năng chính
- ✅ **Tương thích SEB** - Hoạt động trong Safe Exam Browser
- ✅ **Screen sharing** - Chia sẻ màn hình real-time
- ✅ **Remote control** - Điều khiển chuột và bàn phím từ xa
- ✅ **P2P connection** - Kết nối trực tiếp qua WebRTC
- ✅ **Cross-platform** - Windows, macOS, Linux
- ✅ **Modern UI** - Giao diện đẹp, dễ sử dụng

## 🛠️ Cài đặt nhanh

### Bước 1: Kiểm tra yêu cầu hệ thống
- Node.js 16+ ([Download](https://nodejs.org/))
- npm hoặc yarn
- Windows: Visual Studio Build Tools
- macOS: Xcode Command Line Tools
- Linux: build-essential

### Bước 2: Khởi chạy ứng dụng

#### Windows:
```bash
# Cách 1: Sử dụng script tự động
start.bat

# Cách 2: Thủ công
npm start
```

#### macOS/Linux:
```bash
# Cách 1: Sử dụng script tự động
./start.sh

# Cách 2: Thủ công
npm start
```

## 📱 Cách sử dụng

### Chia sẻ màn hình của bạn:
1. Mở ứng dụng
2. Click "Start Sharing" hoặc "Share My Screen"
3. Cho phép quyền truy cập màn hình
4. Chia sẻ ID hiển thị với người muốn kết nối

### Kết nối đến máy tính khác:
1. Nhận ID từ người chia sẻ
2. Nhập ID vào ô "Remote ID"
3. Click "Connect"
4. Chờ kết nối thành công

### Điều khiển từ xa:
- **Chuột**: Click và kéo trên màn hình remote
- **Bàn phím**: Click vào màn hình remote và gõ
- **Cuộn**: Sử dụng scroll wheel
- **Screenshot**: Click icon camera
- **Fullscreen**: Click icon expand

## 🔧 Cấu hình SEB

### Tự động phát hiện SEB:
Ứng dụng tự động phát hiện môi trường SEB và áp dụng các tối ưu hóa:
- Giảm chất lượng video để tăng hiệu suất
- Sử dụng canvas capture thay vì native APIs
- Tắt các tính năng bị hạn chế
- Hiển thị cảnh báo phù hợp

### Cấu hình thủ công:
Chỉnh sửa file `config/seb-config.json` để tùy chỉnh:
```json
{
  "seb": {
    "enabled": true,
    "fallbacks": {
      "screenCapture": {
        "method": "canvas",
        "quality": "medium",
        "frameRate": 15
      }
    }
  }
}
```

## 🚨 Xử lý sự cố

### Lỗi thường gặp:

#### 1. Không thể capture màn hình:
```
Giải pháp:
- Kiểm tra quyền truy cập màn hình
- Chạy với quyền administrator (Windows)
- Bật SEB compatibility mode
```

#### 2. Kết nối thất bại:
```
Giải pháp:
- Kiểm tra firewall
- Đảm bảo signaling server đang chạy
- Thử các STUN server khác
```

#### 3. Hiệu suất kém:
```
Giải pháp:
- Giảm chất lượng video
- Giảm frame rate
- Kiểm tra băng thông mạng
```

#### 4. Input không hoạt động:
```
Giải pháp:
- Cài đặt robotjs đúng cách
- Kiểm tra quyền accessibility (macOS)
- Chạy với quyền administrator
```

## 🔧 Development

### Chạy trong chế độ development:
```bash
npm run dev
```

### Chạy signaling server riêng:
```bash
npm run server
```

### Build ứng dụng:
```bash
npm run build
```

### Chạy tests:
```bash
npm test
```

## 📊 Monitoring

### Kiểm tra trạng thái server:
```
http://localhost:3001/health
```

### Xem danh sách users:
```
http://localhost:3001/users
```

### Debug logs:
```bash
DEBUG=* npm start
```

## 🔒 Bảo mật

### Tính năng bảo mật:
- Mã hóa P2P qua WebRTC
- Không lưu trữ dữ liệu session
- Yêu cầu quyền truy cập rõ ràng
- Firewall friendly

### Best practices:
- Chỉ chia sẻ ID với người tin tưởng
- Đóng ứng dụng sau khi sử dụng
- Kiểm tra kết nối trước khi chia sẻ thông tin nhạy cảm

## 📞 Hỗ trợ

### Báo lỗi:
- Tạo issue trên GitHub
- Cung cấp log files
- Mô tả chi tiết vấn đề

### Tài liệu:
- `README.md` - Hướng dẫn chi tiết
- `config/seb-config.json` - Cấu hình SEB
- Source code có comment đầy đủ

## 🎯 Tips & Tricks

### Tối ưu hiệu suất:
1. Sử dụng chất lượng "Medium" cho SEB
2. Đóng các ứng dụng không cần thiết
3. Sử dụng kết nối mạng ổn định

### SEB specific:
1. Bật SEB compatibility mode
2. Sử dụng canvas capture
3. Giảm frame rate xuống 15fps

### Troubleshooting:
1. Kiểm tra console logs
2. Test kết nối mạng
3. Verify permissions

---

**🎉 Chúc bạn sử dụng thành công!**

Nếu gặp vấn đề, hãy kiểm tra README.md để biết thêm chi tiết hoặc tạo issue trên GitHub.
