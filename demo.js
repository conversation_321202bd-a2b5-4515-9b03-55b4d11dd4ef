// Demo script to showcase the Remote Desktop App
const { spawn } = require('child_process');
const path = require('path');
const http = require('http');

console.log('🎬 Remote Desktop App Demo');
console.log('==========================');

let serverProcess = null;
let electronProcess = null;

// Function to check if server is running
function checkServer() {
    return new Promise((resolve) => {
        const req = http.get('http://localhost:8080/health', (res) => {
            resolve(true);
        });
        req.on('error', () => resolve(false));
        req.setTimeout(2000, () => {
            req.destroy();
            resolve(false);
        });
    });
}

// Function to start signaling server
function startServer() {
    return new Promise((resolve, reject) => {
        console.log('🚀 Starting signaling server...');
        
        serverProcess = spawn('node', ['src/server/signaling-server.js'], {
            stdio: 'pipe',
            cwd: __dirname
        });

        serverProcess.stdout.on('data', (data) => {
            const output = data.toString();
            console.log('📡 Server:', output.trim());
            if (output.includes('running on port')) {
                resolve();
            }
        });

        serverProcess.stderr.on('data', (data) => {
            console.error('❌ Server Error:', data.toString());
        });

        serverProcess.on('error', (error) => {
            console.error('❌ Failed to start server:', error.message);
            reject(error);
        });

        // Timeout after 10 seconds
        setTimeout(() => {
            if (serverProcess && !serverProcess.killed) {
                resolve(); // Assume it started even if we didn't see the message
            }
        }, 10000);
    });
}

// Function to start Electron app
function startElectron() {
    return new Promise((resolve, reject) => {
        console.log('🖥️  Starting Electron app...');
        
        electronProcess = spawn('npm', ['start'], {
            stdio: 'pipe',
            cwd: __dirname,
            shell: true
        });

        electronProcess.stdout.on('data', (data) => {
            const output = data.toString();
            console.log('⚡ Electron:', output.trim());
        });

        electronProcess.stderr.on('data', (data) => {
            const output = data.toString();
            if (!output.includes('DeprecationWarning') && !output.includes('ExtensionLoadWarning')) {
                console.error('⚠️  Electron Warning:', output.trim());
            }
        });

        electronProcess.on('error', (error) => {
            console.error('❌ Failed to start Electron:', error.message);
            reject(error);
        });

        electronProcess.on('close', (code) => {
            console.log(`🔚 Electron exited with code ${code}`);
            resolve();
        });

        // Consider it started after 3 seconds
        setTimeout(() => {
            console.log('✅ Electron app should be running now');
            resolve();
        }, 3000);
    });
}

// Function to show demo instructions
function showInstructions() {
    console.log('\n📋 Demo Instructions:');
    console.log('=====================');
    console.log('');
    console.log('🎯 What you can test:');
    console.log('1. ✅ SEB Detection - The app automatically detects SEB environment');
    console.log('2. ✅ Screen Sharing - Click "Start Sharing" to share your screen');
    console.log('3. ✅ Remote Connection - Use the generated ID to connect from another instance');
    console.log('4. ✅ Remote Control - Control mouse and keyboard remotely');
    console.log('5. ✅ Real-time Video - See live screen updates');
    console.log('');
    console.log('🔧 Testing SEB Mode:');
    console.log('- Open browser with user agent containing "SEB"');
    console.log('- Or modify src/utils/seb-detector.js to force SEB mode');
    console.log('');
    console.log('🌐 Server Endpoints:');
    console.log('- Health Check: http://localhost:8080/health');
    console.log('- Server Info: http://localhost:8080/');
    console.log('- Connected Users: http://localhost:8080/users');
    console.log('');
    console.log('🎮 How to test connection:');
    console.log('1. Start the app (already done)');
    console.log('2. Click "Start Sharing" in the first window');
    console.log('3. Copy the generated ID');
    console.log('4. Open another instance of the app');
    console.log('5. Enter the ID and click "Connect"');
    console.log('6. You should see the shared screen and be able to control it');
    console.log('');
    console.log('🚨 Troubleshooting:');
    console.log('- If screen capture fails, check permissions');
    console.log('- If connection fails, check firewall settings');
    console.log('- For SEB issues, check the console for SEB-specific messages');
    console.log('');
    console.log('📱 Features to test:');
    console.log('- Screenshot capture (camera icon)');
    console.log('- Fullscreen mode (expand icon)');
    console.log('- Quality settings (High/Medium/Low)');
    console.log('- Audio enable/disable');
    console.log('- Clipboard sync (if supported)');
    console.log('');
    console.log('🎉 Enjoy testing the Remote Desktop App!');
    console.log('');
}

// Function to cleanup processes
function cleanup() {
    console.log('\n🧹 Cleaning up...');
    
    if (electronProcess && !electronProcess.killed) {
        console.log('🔚 Stopping Electron...');
        electronProcess.kill('SIGTERM');
    }
    
    if (serverProcess && !serverProcess.killed) {
        console.log('🔚 Stopping server...');
        serverProcess.kill('SIGTERM');
    }
    
    setTimeout(() => {
        process.exit(0);
    }, 2000);
}

// Handle process termination
process.on('SIGINT', cleanup);
process.on('SIGTERM', cleanup);

// Main demo function
async function runDemo() {
    try {
        console.log('🔍 Checking if server is already running...');
        const serverRunning = await checkServer();
        
        if (!serverRunning) {
            await startServer();
            // Wait a bit for server to fully start
            await new Promise(resolve => setTimeout(resolve, 3000));
        } else {
            console.log('✅ Server is already running');
        }
        
        // Verify server is accessible
        const serverReady = await checkServer();
        if (!serverReady) {
            throw new Error('Server failed to start properly');
        }
        
        console.log('✅ Server is ready');
        
        // Start Electron app
        await startElectron();
        
        // Show instructions
        showInstructions();
        
        // Keep the demo running
        console.log('🔄 Demo is running... Press Ctrl+C to stop');
        
    } catch (error) {
        console.error('❌ Demo failed:', error.message);
        cleanup();
    }
}

// Start the demo
runDemo();
