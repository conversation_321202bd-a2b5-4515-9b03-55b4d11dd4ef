const { app, BrowserWindow, ipcMain, desktopCapturer, screen, globalShortcut } = require('electron');
const path = require('path');
const { spawn } = require('child_process');

let mainWindow;
let signalingServer;

// Enable live reload for development
if (process.argv.includes('--dev')) {
  try {
    require('electron-reload')(__dirname, {
      electron: path.join(__dirname, 'node_modules', '.bin', 'electron'),
      hardResetMethod: 'exit'
    });
  } catch (error) {
    console.log('electron-reload not available, skipping...');
  }
}

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true,
      webSecurity: false // Needed for SEB compatibility
    },
    icon: path.join(__dirname, 'assets/icon.png'),
    show: false,
    titleBarStyle: 'default'
  });

  // Load the app
  mainWindow.loadFile('src/renderer/index.html');

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Open DevTools in development
  if (process.argv.includes('--dev')) {
    mainWindow.webContents.openDevTools();
  }

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// App event handlers
app.whenReady().then(() => {
  createWindow();

  // Start signaling server
  startSignalingServer();

  // Register global shortcuts
  registerGlobalShortcuts();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('will-quit', () => {
  // Unregister all shortcuts
  globalShortcut.unregisterAll();

  // Stop signaling server
  if (signalingServer) {
    signalingServer.kill();
  }
});

// Start signaling server
function startSignalingServer() {
  // Check if server is already running
  const http = require('http');

  const checkServer = (port) => {
    return new Promise((resolve) => {
      const req = http.get(`http://localhost:${port}/health`, (res) => {
        resolve(true);
      });
      req.on('error', () => resolve(false));
      req.setTimeout(2000, () => {
        req.destroy();
        resolve(false);
      });
    });
  };

  // Try different ports
  const tryPorts = [8080, 3001, 3002, 8081, 9000];

  const tryStartServer = async () => {
    for (const port of tryPorts) {
      const isRunning = await checkServer(port);
      if (isRunning) {
        console.log(`Server already running on port ${port}`);
        return;
      }
    }

    // Start new server
    try {
      signalingServer = spawn('node', [path.join(__dirname, 'src/server/signaling-server.js')], {
        stdio: 'pipe',
        detached: false,
        env: { ...process.env, PORT: '3001' }
      });

      signalingServer.stdout.on('data', (data) => {
        console.log('Server:', data.toString().trim());
      });

      signalingServer.stderr.on('data', (data) => {
        const error = data.toString().trim();
        if (!error.includes('DeprecationWarning')) {
          console.error('Server Error:', error);
        }
      });

      console.log('Starting signaling server on port 3001...');
    } catch (error) {
      console.error('Failed to start signaling server:', error);
    }
  };

  tryStartServer();
}

// Register global shortcuts
function registerGlobalShortcuts() {
  // Emergency exit shortcut
  globalShortcut.register('CommandOrControl+Shift+Q', () => {
    app.quit();
  });

  // Toggle DevTools
  globalShortcut.register('F12', () => {
    if (mainWindow) {
      mainWindow.webContents.toggleDevTools();
    }
  });
}

// IPC handlers for screen capture
ipcMain.handle('get-sources', async () => {
  try {
    const sources = await desktopCapturer.getSources({
      types: ['window', 'screen'],
      thumbnailSize: { width: 150, height: 150 }
    });
    return sources;
  } catch (error) {
    console.error('Error getting sources:', error);
    return [];
  }
});

// IPC handlers for screen info
ipcMain.handle('get-screen-info', () => {
  const displays = screen.getAllDisplays();
  const primaryDisplay = screen.getPrimaryDisplay();

  return {
    displays,
    primaryDisplay,
    scaleFactor: primaryDisplay.scaleFactor
  };
});

// IPC handler for window management
ipcMain.handle('minimize-window', () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

ipcMain.handle('maximize-window', () => {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow.maximize();
    }
  }
});

ipcMain.handle('close-window', () => {
  if (mainWindow) {
    mainWindow.close();
  }
});

// Handle app protocol for deep linking
app.setAsDefaultProtocolClient('remote-desktop');

// Handle protocol URLs
app.on('open-url', (event, url) => {
  event.preventDefault();
  // Handle remote-desktop:// URLs
  console.log('Protocol URL:', url);
});
