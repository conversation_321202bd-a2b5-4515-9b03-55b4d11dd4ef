#!/bin/bash

echo "Starting Remote Desktop Application..."
echo

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "Error: Node.js is not installed"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

# Check if npm is available
if ! command -v npm &> /dev/null; then
    echo "Error: npm is not available"
    exit 1
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        echo "Error: Failed to install dependencies"
        exit 1
    fi
fi

# Start the signaling server in background
echo "Starting signaling server..."
node src/server/signaling-server.js &
SERVER_PID=$!

# Wait a moment for server to start
sleep 3

# Start the Electron application
echo "Starting Electron application..."
npm start

# Kill the signaling server when Electron exits
kill $SERVER_PID 2>/dev/null
