{"seb": {"enabled": true, "detection": {"userAgentKeywords": ["SEB", "SafeExamBrowser", "seb://", "kiosk"], "protocolCheck": true, "windowPropertiesCheck": true, "apiRestrictionsCheck": true}, "fallbacks": {"screenCapture": {"method": "canvas", "quality": "medium", "frameRate": 15, "resolution": {"width": 1280, "height": 720}}, "clipboard": {"useLocalStorage": true, "syncInterval": 5000}, "fileAccess": {"disabled": true}}, "webrtc": {"iceServers": [{"urls": "stun:stun.l.google.com:19302"}, {"urls": "stun:stun1.l.google.com:19302"}, {"urls": "stun:stun.services.mozilla.com"}], "iceCandidatePoolSize": 5, "bundlePolicy": "balanced", "rtcpMuxPolicy": "require", "iceTransportPolicy": "all"}, "security": {"allowInsecureConnections": true, "disableWebSecurity": true, "allowRunningInsecureContent": true, "ignoreCertificateErrors": true}, "performance": {"video": {"maxWidth": 1280, "maxHeight": 720, "maxFrameRate": 15, "maxBitrate": 1000000}, "rendering": {"hardwareAcceleration": false, "vsync": false, "maxFPS": 15}, "memory": {"maxCacheSize": 52428800, "gcInterval": 30000}}, "ui": {"showSEBWarning": true, "disableDevTools": true, "hideMenuBar": true, "warningMessages": {"screenCapture": "Screen capture may be limited in this environment. Using fallback method.", "clipboard": "Clipboard synchronization may not work properly.", "fileAccess": "File transfer features are disabled.", "performance": "Performance may be reduced in restricted environment."}}}, "standard": {"screenCapture": {"method": "native", "quality": "high", "frameRate": 30, "resolution": {"width": 1920, "height": 1080}}, "webrtc": {"iceServers": [{"urls": "stun:stun.l.google.com:19302"}, {"urls": "stun:stun1.l.google.com:19302"}, {"urls": "stun:stun2.l.google.com:19302"}, {"urls": "stun:stun3.l.google.com:19302"}, {"urls": "stun:stun4.l.google.com:19302"}], "iceCandidatePoolSize": 10}, "performance": {"video": {"maxWidth": 1920, "maxHeight": 1080, "maxFrameRate": 30, "maxBitrate": 5000000}, "rendering": {"hardwareAcceleration": true, "vsync": true, "maxFPS": 60}}}, "server": {"signaling": {"port": 3001, "fallbackPorts": [8080, 80, 3000], "cors": {"origin": "*", "methods": ["GET", "POST"]}}, "stun": {"servers": ["stun:stun.l.google.com:19302", "stun:stun1.l.google.com:19302", "stun:stun.services.mozilla.com"]}}, "logging": {"level": "info", "enableConsole": true, "enableFile": false, "sebSpecific": true}}