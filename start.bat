@echo off
echo Starting Remote Desktop Application...
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if npm is available
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: npm is not available
    pause
    exit /b 1
)

REM Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo Error: Failed to install dependencies
        pause
        exit /b 1
    )
)

REM Start the signaling server in background
echo Starting signaling server...
start /b cmd /c "node src/server/signaling-server.js"

REM Wait a moment for server to start
timeout /t 3 /nobreak >nul

REM Start the Electron application
echo Starting Electron application...
npm start
                                                                                                                                                                                                                                                                            
pause
