// Test script to verify server is working
const http = require('http');

console.log('🧪 Testing Remote Desktop Server...');

// Test server health
function testServerHealth() {
    return new Promise((resolve, reject) => {
        const req = http.get('http://localhost:8080/health', (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    console.log('✅ Server health check passed:', response);
                    resolve(response);
                } catch (error) {
                    console.log('❌ Server health check failed:', error.message);
                    reject(error);
                }
            });
        });

        req.on('error', (error) => {
            console.log('❌ Server connection failed:', error.message);
            reject(error);
        });

        req.setTimeout(5000, () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });
    });
}

// Test server info
function testServerInfo() {
    return new Promise((resolve, reject) => {
        const req = http.get('http://localhost:8080/', (res) => {
            let data = '';
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    console.log('✅ Server info:', response);
                    resolve(response);
                } catch (error) {
                    console.log('❌ Server info failed:', error.message);
                    reject(error);
                }
            });
        });

        req.on('error', (error) => {
            console.log('❌ Server info request failed:', error.message);
            reject(error);
        });

        req.setTimeout(5000, () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });
    });
}

// Run tests
async function runTests() {
    console.log('\n📡 Testing Signaling Server...');
    
    try {
        await testServerHealth();
        await testServerInfo();
        
        console.log('\n🎉 All server tests passed!');
        console.log('\n📋 Next steps:');
        console.log('1. Server is running on http://localhost:8080');
        console.log('2. You can now start the Electron app with: npm start');
        console.log('3. Or use the start scripts: start.bat (Windows) / ./start.sh (Linux/Mac)');
        
    } catch (error) {
        console.log('\n❌ Server tests failed!');
        console.log('💡 Make sure the server is running: node src/server/signaling-server.js');
        console.log('🔧 Or check if port 8080 is available');
    }
}

// Wait a moment for server to start, then run tests
setTimeout(runTests, 2000);
