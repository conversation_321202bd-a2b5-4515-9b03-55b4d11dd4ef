// Cloud deployment script for multiple platforms
const { execSync } = require('child_process');
const fs = require('fs');

console.log('☁️  Cloud Deployment Script');
console.log('===========================');

const config = {
    appName: 'remote-desktop-seb',
    dockerImage: 'remote-desktop-server',
    platforms: {
        railway: 'https://railway.app',
        render: 'https://render.com',
        fly: 'https://fly.io',
        digitalocean: 'https://www.digitalocean.com/products/app-platform'
    }
};

// Create Railway deployment
function createRailwayDeployment() {
    console.log('\n🚂 Creating Railway Deployment...');
    
    const railwayConfig = {
        "name": config.appName,
        "services": {
            "web": {
                "source": {
                    "type": "image",
                    "image": "node:18-alpine"
                },
                "build": {
                    "commands": [
                        "npm ci --only=production"
                    ]
                },
                "deploy": {
                    "startCommand": "node src/server/signaling-server.js"
                },
                "env": {
                    "NODE_ENV": "production",
                    "HOST": "0.0.0.0"
                }
            }
        }
    };
    
    try {
        fs.writeFileSync('railway.json', JSON.stringify(railwayConfig, null, 2));
        console.log('✅ Railway configuration created');
        console.log('📋 Next steps for Railway:');
        console.log('1. Visit https://railway.app');
        console.log('2. Connect your GitHub repository');
        console.log('3. Deploy from the railway.json configuration');
        return true;
    } catch (error) {
        console.log('❌ Failed to create Railway config:', error.message);
        return false;
    }
}

// Create Render deployment
function createRenderDeployment() {
    console.log('\n🎨 Creating Render Deployment...');
    
    const renderConfig = {
        "services": [
            {
                "type": "web",
                "name": config.appName,
                "env": "node",
                "buildCommand": "npm ci --only=production",
                "startCommand": "node src/server/signaling-server.js",
                "envVars": [
                    {
                        "key": "NODE_ENV",
                        "value": "production"
                    },
                    {
                        "key": "HOST",
                        "value": "0.0.0.0"
                    }
                ]
            }
        ]
    };
    
    try {
        fs.writeFileSync('render.yaml', `services:
  - type: web
    name: ${config.appName}
    env: node
    buildCommand: npm ci --only=production
    startCommand: node src/server/signaling-server.js
    envVars:
      - key: NODE_ENV
        value: production
      - key: HOST
        value: 0.0.0.0`);
        
        console.log('✅ Render configuration created');
        console.log('📋 Next steps for Render:');
        console.log('1. Visit https://render.com');
        console.log('2. Connect your GitHub repository');
        console.log('3. Deploy using the render.yaml configuration');
        return true;
    } catch (error) {
        console.log('❌ Failed to create Render config:', error.message);
        return false;
    }
}

// Create Fly.io deployment
function createFlyDeployment() {
    console.log('\n🪰 Creating Fly.io Deployment...');
    
    const flyConfig = `app = "${config.appName}"
primary_region = "iad"

[build]
  image = "node:18-alpine"

[env]
  NODE_ENV = "production"
  HOST = "0.0.0.0"

[[services]]
  http_checks = []
  internal_port = 3001
  processes = ["app"]
  protocol = "tcp"
  script_checks = []

  [services.concurrency]
    hard_limit = 25
    soft_limit = 20
    type = "connections"

  [[services.ports]]
    force_https = true
    handlers = ["http"]
    port = 80

  [[services.ports]]
    handlers = ["tls", "http"]
    port = 443

  [[services.tcp_checks]]
    grace_period = "1s"
    interval = "15s"
    restart_limit = 0
    timeout = "2s"`;
    
    try {
        fs.writeFileSync('fly.toml', flyConfig);
        console.log('✅ Fly.io configuration created');
        console.log('📋 Next steps for Fly.io:');
        console.log('1. Install flyctl: https://fly.io/docs/getting-started/installing-flyctl/');
        console.log('2. Run: flyctl auth login');
        console.log('3. Run: flyctl launch');
        console.log('4. Run: flyctl deploy');
        return true;
    } catch (error) {
        console.log('❌ Failed to create Fly.io config:', error.message);
        return false;
    }
}

// Create DigitalOcean App Platform deployment
function createDigitalOceanDeployment() {
    console.log('\n🌊 Creating DigitalOcean App Platform Deployment...');
    
    const doConfig = {
        "name": config.appName,
        "services": [
            {
                "name": "web",
                "source_dir": "/",
                "github": {
                    "repo": "your-username/remote-desktop-app",
                    "branch": "main"
                },
                "run_command": "node src/server/signaling-server.js",
                "environment_slug": "node-js",
                "instance_count": 1,
                "instance_size_slug": "basic-xxs",
                "envs": [
                    {
                        "key": "NODE_ENV",
                        "value": "production"
                    },
                    {
                        "key": "HOST",
                        "value": "0.0.0.0"
                    }
                ],
                "health_check": {
                    "http_path": "/health"
                }
            }
        ]
    };
    
    try {
        fs.writeFileSync('.do/app.yaml', `name: ${config.appName}
services:
- name: web
  source_dir: /
  run_command: node src/server/signaling-server.js
  environment_slug: node-js
  instance_count: 1
  instance_size_slug: basic-xxs
  envs:
  - key: NODE_ENV
    value: production
  - key: HOST
    value: "0.0.0.0"
  health_check:
    http_path: /health`);
    
        console.log('✅ DigitalOcean configuration created');
        console.log('📋 Next steps for DigitalOcean:');
        console.log('1. Visit https://cloud.digitalocean.com/apps');
        console.log('2. Create new app from GitHub repository');
        console.log('3. Use the .do/app.yaml configuration');
        return true;
    } catch (error) {
        console.log('❌ Failed to create DigitalOcean config:', error.message);
        return false;
    }
}

// Create Netlify Functions deployment (for serverless)
function createNetlifyDeployment() {
    console.log('\n🌐 Creating Netlify Functions Deployment...');
    
    const netlifyConfig = `[build]
  functions = "netlify/functions"
  publish = "src/renderer"

[build.environment]
  NODE_ENV = "production"

[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200`;

    const netlifyFunction = `const { Server } = require('socket.io');
const SignalingServer = require('../../src/server/signaling-server');

exports.handler = async (event, context) => {
  // Serverless function for signaling
  const server = new SignalingServer();
  
  // Handle WebSocket connections
  if (event.httpMethod === 'GET' && event.path === '/health') {
    return {
      statusCode: 200,
      body: JSON.stringify({ status: 'healthy', timestamp: new Date().toISOString() })
    };
  }
  
  return {
    statusCode: 404,
    body: JSON.stringify({ error: 'Not found' })
  };
};`;
    
    try {
        if (!fs.existsSync('netlify/functions')) {
            fs.mkdirSync('netlify/functions', { recursive: true });
        }
        
        fs.writeFileSync('netlify.toml', netlifyConfig);
        fs.writeFileSync('netlify/functions/signaling.js', netlifyFunction);
        
        console.log('✅ Netlify configuration created');
        console.log('📋 Next steps for Netlify:');
        console.log('1. Visit https://netlify.com');
        console.log('2. Connect your GitHub repository');
        console.log('3. Deploy using the netlify.toml configuration');
        return true;
    } catch (error) {
        console.log('❌ Failed to create Netlify config:', error.message);
        return false;
    }
}

// Create deployment instructions
function createDeploymentInstructions() {
    console.log('\n📚 Creating Deployment Instructions...');
    
    const instructions = `# 🚀 Cloud Deployment Instructions

## Available Platforms

### 1. Railway (Recommended)
- **URL**: https://railway.app
- **Config**: railway.json
- **Steps**:
  1. Create account on Railway
  2. Connect GitHub repository
  3. Deploy from railway.json
  4. Set environment variables

### 2. Render
- **URL**: https://render.com
- **Config**: render.yaml
- **Steps**:
  1. Create account on Render
  2. Connect GitHub repository
  3. Deploy as Web Service
  4. Use render.yaml configuration

### 3. Fly.io
- **URL**: https://fly.io
- **Config**: fly.toml
- **Steps**:
  1. Install flyctl CLI
  2. Run: flyctl auth login
  3. Run: flyctl launch
  4. Run: flyctl deploy

### 4. DigitalOcean App Platform
- **URL**: https://www.digitalocean.com/products/app-platform
- **Config**: .do/app.yaml
- **Steps**:
  1. Create account on DigitalOcean
  2. Go to App Platform
  3. Create app from GitHub
  4. Use .do/app.yaml configuration

### 5. Netlify (Serverless)
- **URL**: https://netlify.com
- **Config**: netlify.toml
- **Steps**:
  1. Create account on Netlify
  2. Connect GitHub repository
  3. Deploy with Functions enabled

## Environment Variables

All platforms need these environment variables:
- \`NODE_ENV=production\`
- \`HOST=0.0.0.0\`
- \`PORT\` (automatically set by most platforms)

## Post-Deployment

1. **Test the deployment**:
   \`\`\`bash
   curl https://your-app-url.com/health
   \`\`\`

2. **Update client configuration**:
   - Edit \`src/renderer/app.js\`
   - Update server URL to your deployed URL

3. **Build and distribute client**:
   \`\`\`bash
   npm run build-dist
   \`\`\`

## Monitoring

- Check application logs
- Monitor performance metrics
- Set up alerts for downtime
- Test SEB compatibility regularly

## Support

For deployment issues:
- Check platform documentation
- Review application logs
- Test locally first
- Contact platform support if needed
`;
    
    try {
        fs.writeFileSync('DEPLOYMENT_INSTRUCTIONS.md', instructions);
        console.log('✅ Deployment instructions created');
        return true;
    } catch (error) {
        console.log('❌ Failed to create instructions:', error.message);
        return false;
    }
}

// Main deployment function
async function deploy() {
    console.log('🚀 Starting cloud deployment configuration...\n');
    
    const deployments = [
        { name: 'Railway', fn: createRailwayDeployment },
        { name: 'Render', fn: createRenderDeployment },
        { name: 'Fly.io', fn: createFlyDeployment },
        { name: 'DigitalOcean', fn: createDigitalOceanDeployment },
        { name: 'Netlify', fn: createNetlifyDeployment },
        { name: 'Instructions', fn: createDeploymentInstructions }
    ];
    
    let successCount = 0;
    
    for (const deployment of deployments) {
        try {
            const success = deployment.fn();
            if (success) successCount++;
        } catch (error) {
            console.log(`❌ ${deployment.name} deployment config failed:`, error.message);
        }
    }
    
    console.log('\n' + '='.repeat(50));
    console.log(`🎉 Cloud deployment configurations created!`);
    console.log(`✅ ${successCount}/${deployments.length} configurations successful`);
    
    console.log('\n📋 Available deployment options:');
    console.log('- Railway: railway.json');
    console.log('- Render: render.yaml');
    console.log('- Fly.io: fly.toml');
    console.log('- DigitalOcean: .do/app.yaml');
    console.log('- Netlify: netlify.toml');
    
    console.log('\n📚 Read DEPLOYMENT_INSTRUCTIONS.md for detailed steps');
    console.log('\n🎯 Recommended: Start with Railway for easiest deployment');
}

// Run deployment if script is executed directly
if (require.main === module) {
    deploy().catch(error => {
        console.error('❌ Deployment configuration failed:', error);
        process.exit(1);
    });
}

module.exports = { deploy, config };
