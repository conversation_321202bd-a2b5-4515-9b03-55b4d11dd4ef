# 🚀 Deployment Guide - Remote Desktop App

## 📋 Overview
Hướng dẫn triển khai ứng dụng Remote Desktop cho môi trường production và SEB.

---

## 🏗️ Build for Production

### 1. Build Electron App
```bash
# Build for current platform
npm run build

# Build for all platforms
npm run build:all

# Build for specific platforms
npm run build:win    # Windows
npm run build:mac    # macOS  
npm run build:linux  # Linux
```

### 2. Output Files
```
dist/
├── Remote Desktop App Setup.exe     # Windows installer
├── Remote Desktop App.dmg           # macOS installer
└── Remote Desktop App.AppImage      # Linux installer
```

---

## 🌐 Server Deployment

### 1. Standalone Signaling Server
```bash
# Production server
NODE_ENV=production PORT=8080 node src/server/signaling-server.js

# With PM2 (recommended)
pm2 start src/server/signaling-server.js --name "remote-desktop-server"
pm2 startup
pm2 save
```

### 2. Docker Deployment
```dockerfile
# Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY src/ ./src/
COPY config/ ./config/
EXPOSE 8080
CMD ["node", "src/server/signaling-server.js"]
```

```bash
# Build and run
docker build -t remote-desktop-server .
docker run -d -p 8080:8080 --name remote-desktop remote-desktop-server
```

### 3. Cloud Deployment (AWS/Azure/GCP)
```yaml
# docker-compose.yml
version: '3.8'
services:
  remote-desktop-server:
    build: .
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - PORT=8080
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```

---

## 🔧 SEB Environment Setup

### 1. SEB Configuration File
```json
{
  "allowedDisplays": 1,
  "allowQuit": false,
  "allowReload": false,
  "browserWindowAllowReload": false,
  "enableAltEsc": false,
  "enableAltF4": false,
  "enableAltTab": false,
  "enableCtrlAltDel": false,
  "enableF1": false,
  "enableF2": false,
  "enableF3": false,
  "enableF4": false,
  "enableF5": false,
  "enableF6": false,
  "enableF7": false,
  "enableF8": false,
  "enableF9": false,
  "enableF10": false,
  "enableF11": false,
  "enableF12": false,
  "enablePrintScreen": false,
  "enableRightMouse": false,
  "enableZoomPage": false,
  "enableZoomText": false,
  "exitKey1": 2,
  "exitKey2": 10,
  "exitKey3": 5,
  "hashedQuitPassword": "",
  "hideBrowserWindowToolbar": true,
  "killExplorerShell": true,
  "kiosk": true,
  "mainBrowserWindowHeight": "100%",
  "mainBrowserWindowPositioning": 1,
  "mainBrowserWindowWidth": "100%",
  "monitorProcesses": true,
  "newBrowserWindowByLinkBlockForeign": true,
  "newBrowserWindowByLinkPolicy": 2,
  "newBrowserWindowByScriptBlockForeign": true,
  "newBrowserWindowByScriptPolicy": 2,
  "restartExamPasswordProtected": true,
  "sebServicePolicy": 1,
  "showMenuBar": false,
  "showReloadButton": false,
  "showTaskBar": false,
  "startURL": "file:///path/to/remote-desktop-app/src/renderer/index.html",
  "touchOptimized": false,
  "urlFilterEnable": true,
  "urlFilterEnableContentFilter": false
}
```

### 2. SEB App Integration
```bash
# Package app for SEB
electron-builder --config.nsis.oneClick=false --config.nsis.allowToChangeInstallationDirectory=true

# Create SEB-compatible executable
electron-packager . remote-desktop-seb --platform=win32 --arch=x64 --out=dist-seb/
```

---

## 🔒 Security Configuration

### 1. Production Security
```javascript
// config/production.json
{
  "security": {
    "encryption": true,
    "allowInsecureConnections": false,
    "maxConnections": 50,
    "sessionTimeout": 3600000,
    "rateLimit": {
      "windowMs": 900000,
      "max": 100
    }
  }
}
```

### 2. Firewall Rules
```bash
# Windows Firewall
netsh advfirewall firewall add rule name="Remote Desktop App" dir=in action=allow protocol=TCP localport=8080

# Linux iptables
iptables -A INPUT -p tcp --dport 8080 -j ACCEPT

# UFW (Ubuntu)
ufw allow 8080/tcp
```

### 3. SSL/TLS Setup
```javascript
// For HTTPS signaling server
const https = require('https');
const fs = require('fs');

const options = {
  key: fs.readFileSync('path/to/private-key.pem'),
  cert: fs.readFileSync('path/to/certificate.pem')
};

const server = https.createServer(options, app);
```

---

## 📊 Monitoring & Logging

### 1. Application Monitoring
```bash
# PM2 monitoring
pm2 monit

# Logs
pm2 logs remote-desktop-server

# Restart
pm2 restart remote-desktop-server
```

### 2. Health Checks
```bash
# Server health
curl http://localhost:8080/health

# Connection stats
curl http://localhost:8080/users
```

### 3. Log Configuration
```javascript
// config/logging.json
{
  "level": "info",
  "enableFile": true,
  "maxFileSize": "10MB",
  "maxFiles": 5,
  "logDirectory": "/var/log/remote-desktop"
}
```

---

## 🌍 Network Configuration

### 1. STUN/TURN Servers
```javascript
// For production, use dedicated TURN servers
{
  "iceServers": [
    { "urls": "stun:your-stun-server.com:3478" },
    {
      "urls": "turn:your-turn-server.com:3478",
      "username": "username",
      "credential": "password"
    }
  ]
}
```

### 2. Load Balancing
```nginx
# nginx.conf
upstream remote_desktop {
    server 127.0.0.1:8080;
    server 127.0.0.1:8081;
    server 127.0.0.1:8082;
}

server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://remote_desktop;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

---

## 📱 Client Distribution

### 1. Windows Distribution
```bash
# Create installer
npm run build:win

# Sign executable (optional)
signtool sign /f certificate.p12 /p password /t http://timestamp.digicert.com "dist/Remote Desktop App Setup.exe"
```

### 2. macOS Distribution
```bash
# Create DMG
npm run build:mac

# Notarize (for App Store)
xcrun altool --notarize-app --primary-bundle-id "com.yourcompany.remote-desktop" --username "your-apple-id" --password "app-specific-password" --file "dist/Remote Desktop App.dmg"
```

### 3. Linux Distribution
```bash
# Create AppImage
npm run build:linux

# Create DEB package
electron-builder --linux deb

# Create RPM package  
electron-builder --linux rpm
```

---

## 🔄 Update Mechanism

### 1. Auto-updater Setup
```javascript
// main.js
const { autoUpdater } = require('electron-updater');

autoUpdater.checkForUpdatesAndNotify();

autoUpdater.on('update-available', () => {
  // Notify user
});

autoUpdater.on('update-downloaded', () => {
  // Prompt user to restart
});
```

### 2. Update Server
```javascript
// Simple update server
app.get('/update/:platform/:version', (req, res) => {
  const { platform, version } = req.params;
  // Check for updates and return update info
});
```

---

## 🎯 Performance Optimization

### 1. Production Settings
```javascript
{
  "performance": {
    "maxMemoryUsage": 512,
    "gcInterval": 60000,
    "statsInterval": 5000,
    "compression": true
  }
}
```

### 2. CDN Configuration
```javascript
// Serve static assets from CDN
{
  "cdn": {
    "enabled": true,
    "baseUrl": "https://cdn.yourcompany.com/remote-desktop/"
  }
}
```

---

## ✅ Deployment Checklist

### Pre-deployment
- [ ] Code tested in target environment
- [ ] Dependencies updated and secure
- [ ] Configuration files prepared
- [ ] SSL certificates obtained
- [ ] Firewall rules configured
- [ ] Monitoring setup ready

### Deployment
- [ ] Server deployed and running
- [ ] Health checks passing
- [ ] Client apps built and signed
- [ ] Distribution channels ready
- [ ] Documentation updated
- [ ] Support team notified

### Post-deployment
- [ ] Monitor application performance
- [ ] Check error logs
- [ ] Verify user connections
- [ ] Test SEB compatibility
- [ ] Collect user feedback
- [ ] Plan updates and improvements

---

## 🆘 Troubleshooting

### Common Issues
1. **Port conflicts** - Change server port in config
2. **Permission errors** - Run with appropriate privileges
3. **Network issues** - Check firewall and STUN/TURN servers
4. **SEB compatibility** - Verify SEB configuration file
5. **Performance issues** - Adjust quality settings

### Support Contacts
- Technical Support: <EMAIL>
- Documentation: docs.yourcompany.com
- GitHub Issues: github.com/yourcompany/remote-desktop

---

**🎉 Deployment Complete! Your Remote Desktop App is now ready for production use.**
