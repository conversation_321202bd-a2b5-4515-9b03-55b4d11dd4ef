{"app": {"name": "Remote Desktop Pro", "version": "1.0.0", "environment": "production"}, "server": {"signaling": {"port": 8080, "host": "0.0.0.0", "cors": {"origin": "*", "methods": ["GET", "POST"], "credentials": true}, "compression": true, "rateLimit": {"windowMs": 900000, "max": 100}}, "stun": {"servers": ["stun:stun.l.google.com:19302", "stun:stun1.l.google.com:19302", "stun:stun2.l.google.com:19302", "stun:stun.services.mozilla.com"]}, "turn": {"enabled": false, "servers": []}}, "webrtc": {"iceServers": [{"urls": "stun:stun.l.google.com:19302"}, {"urls": "stun:stun1.l.google.com:19302"}, {"urls": "stun:stun2.l.google.com:19302"}, {"urls": "stun:stun.services.mozilla.com"}], "iceCandidatePoolSize": 10, "bundlePolicy": "balanced", "rtcpMuxPolicy": "require", "iceTransportPolicy": "all"}, "video": {"quality": {"high": {"width": 1920, "height": 1080, "frameRate": 30, "bitrate": 5000000}, "medium": {"width": 1280, "height": 720, "frameRate": 24, "bitrate": 2500000}, "low": {"width": 854, "height": 480, "frameRate": 15, "bitrate": 1000000}}, "defaultQuality": "medium", "adaptiveQuality": true}, "audio": {"enabled": true, "sampleRate": 44100, "channels": 2, "bitrate": 128000}, "security": {"encryption": true, "allowInsecureConnections": false, "maxConnections": 10, "sessionTimeout": 3600000, "idLength": 9}, "performance": {"maxMemoryUsage": 512, "gcInterval": 60000, "statsInterval": 5000, "heartbeatInterval": 30000}, "features": {"screenCapture": true, "remoteControl": true, "fileTransfer": false, "clipboard": true, "chat": false, "recording": false}, "ui": {"theme": "dark", "showAdvancedSettings": false, "autoHideControls": true, "showConnectionStats": true, "notifications": true}, "logging": {"level": "info", "enableConsole": true, "enableFile": true, "maxFileSize": "10MB", "maxFiles": 5, "logDirectory": "logs"}, "seb": {"autoDetect": true, "forceMode": false, "fallbacks": {"screenCapture": {"method": "canvas", "quality": "medium", "frameRate": 15}, "clipboard": {"useLocalStorage": true, "syncInterval": 5000}}, "restrictions": {"disableDevTools": true, "hideMenuBar": true, "disableFileAccess": true}, "warnings": {"show": true, "duration": 10000}}, "network": {"timeout": {"connection": 30000, "response": 10000, "keepAlive": 60000}, "retry": {"attempts": 3, "delay": 1000, "backoff": 2}, "bandwidth": {"test": true, "adaptive": true, "minBitrate": 500000, "maxBitrate": 10000000}}, "monitoring": {"enabled": true, "metrics": {"connection": true, "performance": true, "errors": true, "usage": true}, "alerts": {"highLatency": 500, "lowFPS": 10, "connectionLoss": true}}}