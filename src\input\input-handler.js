const robot = require('robotjs');

class InputHandler {
    constructor() {
        this.isEnabled = false;
        this.screenSize = null;
        this.scaleFactor = 1;
        this.mousePosition = { x: 0, y: 0 };
        this.pressedKeys = new Set();
        this.mouseButtons = new Set();
        
        this.initialize();
    }

    async initialize() {
        try {
            // Get screen information
            this.screenSize = robot.getScreenSize();
            console.log('Screen size:', this.screenSize);
            
            // Set robot settings for better performance
            robot.setMouseDelay(2);
            robot.setKeyboardDelay(2);
            
            // Enable smooth mouse movement
            robot.setMouseDelay(0);
            
        } catch (error) {
            console.error('Error initializing input handler:', error);
        }
    }

    enable() {
        this.isEnabled = true;
        console.log('Input handler enabled');
    }

    disable() {
        this.isEnabled = false;
        this.releaseAllInputs();
        console.log('Input handler disabled');
    }

    setScreenScale(remoteWidth, remoteHeight, localWidth, localHeight) {
        this.scaleX = this.screenSize.width / remoteWidth;
        this.scaleY = this.screenSize.height / remoteHeight;
        console.log('Screen scale set:', { scaleX: this.scaleX, scaleY: this.scaleY });
    }

    handleInputEvent(inputData) {
        if (!this.isEnabled) return;

        try {
            switch (inputData.type) {
                case 'mousemove':
                    this.handleMouseMove(inputData.data);
                    break;
                case 'mousedown':
                    this.handleMouseDown(inputData.data);
                    break;
                case 'mouseup':
                    this.handleMouseUp(inputData.data);
                    break;
                case 'wheel':
                    this.handleMouseWheel(inputData.data);
                    break;
                case 'keydown':
                    this.handleKeyDown(inputData.data);
                    break;
                case 'keyup':
                    this.handleKeyUp(inputData.data);
                    break;
                case 'click':
                    this.handleClick(inputData.data);
                    break;
                case 'doubleclick':
                    this.handleDoubleClick(inputData.data);
                    break;
                default:
                    console.log('Unknown input event:', inputData.type);
            }
        } catch (error) {
            console.error('Error handling input event:', error);
        }
    }

    handleMouseMove(data) {
        const x = Math.round(data.x * this.scaleX);
        const y = Math.round(data.y * this.scaleY);
        
        // Ensure coordinates are within screen bounds
        const clampedX = Math.max(0, Math.min(x, this.screenSize.width - 1));
        const clampedY = Math.max(0, Math.min(y, this.screenSize.height - 1));
        
        robot.moveMouse(clampedX, clampedY);
        this.mousePosition = { x: clampedX, y: clampedY };
    }

    handleMouseDown(data) {
        const x = Math.round(data.x * this.scaleX);
        const y = Math.round(data.y * this.scaleY);
        const button = this.mapMouseButton(data.button);
        
        if (button) {
            robot.moveMouse(x, y);
            robot.mouseToggle('down', button);
            this.mouseButtons.add(button);
        }
    }

    handleMouseUp(data) {
        const x = Math.round(data.x * this.scaleX);
        const y = Math.round(data.y * this.scaleY);
        const button = this.mapMouseButton(data.button);
        
        if (button) {
            robot.moveMouse(x, y);
            robot.mouseToggle('up', button);
            this.mouseButtons.delete(button);
        }
    }

    handleClick(data) {
        const x = Math.round(data.x * this.scaleX);
        const y = Math.round(data.y * this.scaleY);
        const button = this.mapMouseButton(data.button);
        
        if (button) {
            robot.moveMouse(x, y);
            robot.mouseClick(button);
        }
    }

    handleDoubleClick(data) {
        const x = Math.round(data.x * this.scaleX);
        const y = Math.round(data.y * this.scaleY);
        const button = this.mapMouseButton(data.button);
        
        if (button) {
            robot.moveMouse(x, y);
            robot.mouseClick(button, true); // Double click
        }
    }

    handleMouseWheel(data) {
        const x = Math.round(data.x * this.scaleX);
        const y = Math.round(data.y * this.scaleY);
        
        robot.moveMouse(x, y);
        
        // Convert wheel delta to scroll direction
        const scrollDirection = data.deltaY > 0 ? 'down' : 'up';
        const scrollMagnitude = Math.abs(data.deltaY);
        
        // Perform scroll
        for (let i = 0; i < Math.min(scrollMagnitude / 100, 5); i++) {
            robot.scrollMouse(1, scrollDirection);
        }
    }

    handleKeyDown(data) {
        const key = this.mapKey(data);
        
        if (key) {
            try {
                // Handle modifier keys
                if (this.isModifierKey(key)) {
                    robot.keyToggle(key, 'down');
                    this.pressedKeys.add(key);
                } else {
                    // Handle regular keys with modifiers
                    const modifiers = this.getActiveModifiers(data);
                    if (modifiers.length > 0) {
                        robot.keyTap(key, modifiers);
                    } else {
                        robot.keyToggle(key, 'down');
                        this.pressedKeys.add(key);
                    }
                }
            } catch (error) {
                console.error('Error handling key down:', error);
            }
        }
    }

    handleKeyUp(data) {
        const key = this.mapKey(data);
        
        if (key && this.pressedKeys.has(key)) {
            try {
                robot.keyToggle(key, 'up');
                this.pressedKeys.delete(key);
            } catch (error) {
                console.error('Error handling key up:', error);
            }
        }
    }

    mapMouseButton(button) {
        const buttonMap = {
            0: 'left',
            1: 'middle',
            2: 'right'
        };
        return buttonMap[button] || 'left';
    }

    mapKey(data) {
        // Handle special keys
        const specialKeys = {
            'Enter': 'enter',
            'Escape': 'escape',
            'Backspace': 'backspace',
            'Tab': 'tab',
            'Space': 'space',
            'ArrowUp': 'up',
            'ArrowDown': 'down',
            'ArrowLeft': 'left',
            'ArrowRight': 'right',
            'Home': 'home',
            'End': 'end',
            'PageUp': 'pageup',
            'PageDown': 'pagedown',
            'Insert': 'insert',
            'Delete': 'delete',
            'CapsLock': 'capslock',
            'NumLock': 'numlock',
            'ScrollLock': 'scrolllock',
            'PrintScreen': 'printscreen',
            'Pause': 'pause'
        };

        // Handle function keys
        for (let i = 1; i <= 12; i++) {
            specialKeys[`F${i}`] = `f${i}`;
        }

        // Handle modifier keys
        const modifierKeys = {
            'Control': 'control',
            'Alt': 'alt',
            'Shift': 'shift',
            'Meta': 'command' // Windows key / Cmd key
        };

        // Check for special keys first
        if (specialKeys[data.key]) {
            return specialKeys[data.key];
        }

        // Check for modifier keys
        if (modifierKeys[data.key]) {
            return modifierKeys[data.key];
        }

        // Handle regular characters
        if (data.key.length === 1) {
            return data.key.toLowerCase();
        }

        // Fallback
        return null;
    }

    isModifierKey(key) {
        const modifiers = ['control', 'alt', 'shift', 'command'];
        return modifiers.includes(key);
    }

    getActiveModifiers(data) {
        const modifiers = [];
        
        if (data.ctrlKey) modifiers.push('control');
        if (data.altKey) modifiers.push('alt');
        if (data.shiftKey) modifiers.push('shift');
        if (data.metaKey) modifiers.push('command');
        
        return modifiers;
    }

    releaseAllInputs() {
        // Release all pressed keys
        for (const key of this.pressedKeys) {
            try {
                robot.keyToggle(key, 'up');
            } catch (error) {
                console.error('Error releasing key:', key, error);
            }
        }
        this.pressedKeys.clear();

        // Release all mouse buttons
        for (const button of this.mouseButtons) {
            try {
                robot.mouseToggle('up', button);
            } catch (error) {
                console.error('Error releasing mouse button:', button, error);
            }
        }
        this.mouseButtons.clear();
    }

    // Utility methods
    getMousePosition() {
        return { ...this.mousePosition };
    }

    getScreenSize() {
        return { ...this.screenSize };
    }

    isInputEnabled() {
        return this.isEnabled;
    }

    // Clipboard operations
    async setClipboard(text) {
        try {
            // Use robot.js clipboard functionality if available
            if (robot.setClipboard) {
                robot.setClipboard(text);
                return true;
            }
            return false;
        } catch (error) {
            console.error('Error setting clipboard:', error);
            return false;
        }
    }

    async getClipboard() {
        try {
            // Use robot.js clipboard functionality if available
            if (robot.getClipboard) {
                return robot.getClipboard();
            }
            return null;
        } catch (error) {
            console.error('Error getting clipboard:', error);
            return null;
        }
    }

    // Screen capture for cursor position
    captureScreen(x, y, width, height) {
        try {
            return robot.screen.capture(x, y, width, height);
        } catch (error) {
            console.error('Error capturing screen:', error);
            return null;
        }
    }

    // Get pixel color at position
    getPixelColor(x, y) {
        try {
            return robot.getPixelColor(x, y);
        } catch (error) {
            console.error('Error getting pixel color:', error);
            return null;
        }
    }
}

module.exports = InputHandler;
