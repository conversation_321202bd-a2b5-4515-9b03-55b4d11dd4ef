{"server": {"name": "Remote Desktop SEB Server", "version": "1.0.0", "environment": "production", "host": "0.0.0.0", "port": "${PORT:-3001}", "publicUrl": "https://remote-desktop-seb.herokuapp.com", "cors": {"origin": "*", "methods": ["GET", "POST", "OPTIONS"], "allowedHeaders": ["Content-Type", "Authorization"], "credentials": true}, "compression": true, "rateLimit": {"windowMs": 900000, "max": 1000, "message": "Too many requests from this IP"}}, "webrtc": {"iceServers": [{"urls": "stun:stun.l.google.com:19302"}, {"urls": "stun:stun1.l.google.com:19302"}, {"urls": "stun:stun2.l.google.com:19302"}, {"urls": "stun:stun.services.mozilla.com"}, {"urls": "stun:stun.cloudflare.com:3478"}], "iceCandidatePoolSize": 10, "bundlePolicy": "balanced", "rtcpMuxPolicy": "require", "iceTransportPolicy": "all"}, "seb": {"autoDetect": true, "optimizations": {"video": {"maxWidth": 1280, "maxHeight": 720, "maxFrameRate": 15, "maxBitrate": 1000000}, "network": {"timeout": 30000, "retryAttempts": 5, "retryDelay": 2000}, "performance": {"maxConnections": 100, "connectionTimeout": 60000, "heartbeatInterval": 30000}}, "fallbacks": {"screenCapture": "canvas", "clipboard": "localStorage", "fileTransfer": "disabled"}}, "security": {"encryption": true, "allowInsecureConnections": false, "maxConnectionsPerIP": 10, "sessionTimeout": 3600000, "idLength": 9, "bannedIPs": [], "allowedOrigins": ["https://remote-desktop-seb.herokuapp.com", "http://localhost:*", "file://*"]}, "monitoring": {"enabled": true, "metrics": {"connections": true, "performance": true, "errors": true, "usage": true}, "alerts": {"highLatency": 1000, "lowFPS": 5, "connectionFailures": 10, "memoryUsage": 80}, "logging": {"level": "info", "enableConsole": true, "enableFile": false, "maxFileSize": "10MB", "maxFiles": 3}}, "features": {"multiUser": true, "screenSharing": true, "remoteControl": true, "fileTransfer": false, "chat": false, "recording": false, "screenshots": true, "qualityControl": true}, "limits": {"maxUsers": 1000, "maxSessionsPerUser": 5, "maxSessionDuration": 14400000, "maxBandwidthPerSession": 5000000, "maxScreenResolution": "1920x1080"}, "cleanup": {"inactiveSessionTimeout": 300000, "disconnectedUserTimeout": 60000, "tempFileCleanup": 3600000, "logRotation": 86400000}}