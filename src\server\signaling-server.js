const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');

class SignalingServer {
    constructor() {
        this.app = express();
        this.server = http.createServer(this.app);
        this.io = socketIo(this.server, {
            cors: {
                origin: "*",
                methods: ["GET", "POST"]
            }
        });

        this.connectedUsers = new Map();
        this.setupMiddleware();
        this.setupRoutes();
        this.setupSocketHandlers();
    }

    setupMiddleware() {
        this.app.use(cors());
        this.app.use(express.json());
        this.app.use(express.static('public'));
    }

    setupRoutes() {
        this.app.get('/', (req, res) => {
            res.json({
                message: 'Remote Desktop Signaling Server',
                version: '1.0.0',
                connectedUsers: this.connectedUsers.size
            });
        });

        this.app.get('/health', (req, res) => {
            res.json({
                status: 'healthy',
                uptime: process.uptime(),
                connectedUsers: this.connectedUsers.size
            });
        });

        this.app.get('/users', (req, res) => {
            const users = Array.from(this.connectedUsers.values()).map(user => ({
                id: user.id,
                connectedAt: user.connectedAt,
                isSharing: user.isSharing
            }));
            res.json(users);
        });
    }

    setupSocketHandlers() {
        this.io.on('connection', (socket) => {
            console.log(`User connected: ${socket.id}`);

            // Generate unique user ID
            const userId = this.generateUserId();

            // Store user info
            this.connectedUsers.set(socket.id, {
                id: userId,
                socketId: socket.id,
                connectedAt: new Date(),
                isSharing: false
            });

            // Send user ID to client
            socket.emit('id-assigned', userId);

            // Handle offer
            socket.on('offer', (data) => {
                console.log(`Offer from ${data.from} to ${data.to}`);
                const targetSocket = this.findSocketByUserId(data.to);
                if (targetSocket) {
                    targetSocket.emit('offer', data);
                } else {
                    socket.emit('error', 'Target user not found');
                }
            });

            // Handle answer
            socket.on('answer', (data) => {
                console.log(`Answer from ${data.from} to ${data.to}`);
                const targetSocket = this.findSocketByUserId(data.to);
                if (targetSocket) {
                    targetSocket.emit('answer', data);
                } else {
                    socket.emit('error', 'Target user not found');
                }
            });

            // Handle ICE candidates
            socket.on('ice-candidate', (data) => {
                const targetSocket = this.findSocketByUserId(data.to);
                if (targetSocket) {
                    targetSocket.emit('ice-candidate', data);
                }
            });

            // Handle sharing status
            socket.on('sharing-started', () => {
                const user = this.connectedUsers.get(socket.id);
                if (user) {
                    user.isSharing = true;
                    console.log(`User ${user.id} started sharing`);
                }
            });

            socket.on('sharing-stopped', () => {
                const user = this.connectedUsers.get(socket.id);
                if (user) {
                    user.isSharing = false;
                    console.log(`User ${user.id} stopped sharing`);
                }
            });

            // Handle disconnect
            socket.on('disconnect', () => {
                const user = this.connectedUsers.get(socket.id);
                if (user) {
                    console.log(`User disconnected: ${user.id}`);

                    // Notify other users about disconnection
                    socket.broadcast.emit('user-disconnected', user.id);

                    // Remove user from connected users
                    this.connectedUsers.delete(socket.id);
                }
            });

            // Handle ping for latency measurement
            socket.on('ping', (timestamp) => {
                socket.emit('pong', timestamp);
            });

            // Handle custom messages
            socket.on('message', (data) => {
                const targetSocket = this.findSocketByUserId(data.to);
                if (targetSocket) {
                    targetSocket.emit('message', {
                        from: this.connectedUsers.get(socket.id).id,
                        data: data.message
                    });
                }
            });

            // Handle room joining (for future group features)
            socket.on('join-room', (roomId) => {
                socket.join(roomId);
                socket.to(roomId).emit('user-joined', {
                    userId: this.connectedUsers.get(socket.id).id,
                    socketId: socket.id
                });
            });

            socket.on('leave-room', (roomId) => {
                socket.leave(roomId);
                socket.to(roomId).emit('user-left', {
                    userId: this.connectedUsers.get(socket.id).id,
                    socketId: socket.id
                });
            });
        });
    }

    findSocketByUserId(userId) {
        for (const [socketId, user] of this.connectedUsers) {
            if (user.id === userId) {
                return this.io.sockets.sockets.get(socketId);
            }
        }
        return null;
    }

    generateUserId() {
        // Generate a unique 9-character ID
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < 9; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }

        // Ensure uniqueness
        const existingIds = Array.from(this.connectedUsers.values()).map(user => user.id);
        if (existingIds.includes(result)) {
            return this.generateUserId(); // Recursive call if ID already exists
        }

        return result;
    }

    start(port = 3001) {
        this.server.listen(port, () => {
            console.log(`Signaling server running on port ${port}`);
            console.log(`Health check: http://localhost:${port}/health`);
        });

        // Graceful shutdown
        process.on('SIGTERM', () => {
            console.log('SIGTERM received, shutting down gracefully');
            this.server.close(() => {
                console.log('Server closed');
                process.exit(0);
            });
        });

        process.on('SIGINT', () => {
            console.log('SIGINT received, shutting down gracefully');
            this.server.close(() => {
                console.log('Server closed');
                process.exit(0);
            });
        });

        // Handle uncaught exceptions
        process.on('uncaughtException', (error) => {
            console.error('Uncaught Exception:', error);
            process.exit(1);
        });

        process.on('unhandledRejection', (reason, promise) => {
            console.error('Unhandled Rejection at:', promise, 'reason:', reason);
            process.exit(1);
        });
    }

    // Utility methods for monitoring
    getStats() {
        return {
            connectedUsers: this.connectedUsers.size,
            uptime: process.uptime(),
            memoryUsage: process.memoryUsage(),
            users: Array.from(this.connectedUsers.values()).map(user => ({
                id: user.id,
                connectedAt: user.connectedAt,
                isSharing: user.isSharing
            }))
        };
    }

    broadcastToAll(event, data) {
        this.io.emit(event, data);
    }

    sendToUser(userId, event, data) {
        const socket = this.findSocketByUserId(userId);
        if (socket) {
            socket.emit(event, data);
            return true;
        }
        return false;
    }
}

// Start the server if this file is run directly
if (require.main === module) {
    const server = new SignalingServer();
    const port = process.env.PORT || 3001;
    server.start(port);
}

module.exports = SignalingServer;
