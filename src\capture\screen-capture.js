const { ipc<PERSON><PERSON><PERSON> } = require('electron');

class ScreenCapture {
    constructor() {
        this.isCapturing = false;
        this.captureStream = null;
        this.canvas = null;
        this.ctx = null;
        this.frameRate = 30;
        this.quality = 'medium';
        this.sebCompatible = true;
    }

    async initialize() {
        // Check if running in SEB environment
        this.sebCompatible = this.detectSEBEnvironment();
        console.log('SEB Compatible mode:', this.sebCompatible);
    }

    detectSEBEnvironment() {
        // Check for SEB-specific indicators
        const userAgent = navigator.userAgent;
        const isSEB = userAgent.includes('SEB') || 
                     userAgent.includes('SafeExamBrowser') ||
                     window.location.protocol === 'seb:' ||
                     navigator.userAgent.includes('kiosk');
        
        return isSEB;
    }

    async getSources() {
        try {
            if (this.sebCompatible) {
                // Use SEB-compatible method
                return await this.getSEBCompatibleSources();
            } else {
                // Use standard Electron method
                return await ipc<PERSON>enderer.invoke('get-sources');
            }
        } catch (error) {
            console.error('Error getting sources:', error);
            return [];
        }
    }

    async getSEBCompatibleSources() {
        // Fallback method for SEB environment
        // This uses alternative APIs that might work in restricted environments
        try {
            // Try to use getDisplayMedia if available
            if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
                const stream = await navigator.mediaDevices.getDisplayMedia({
                    video: {
                        mediaSource: 'screen',
                        width: { ideal: 1920, max: 1920 },
                        height: { ideal: 1080, max: 1080 },
                        frameRate: { ideal: this.frameRate, max: 60 }
                    },
                    audio: false
                });
                
                return [{
                    id: 'screen:0:0',
                    name: 'Entire Screen',
                    thumbnail: await this.createThumbnailFromStream(stream)
                }];
            }
            
            // Fallback to canvas-based capture
            return await this.getCanvasBasedSources();
            
        } catch (error) {
            console.error('SEB compatible source detection failed:', error);
            return [];
        }
    }

    async getCanvasBasedSources() {
        // Alternative method using canvas and screen APIs
        try {
            const screenInfo = await ipcRenderer.invoke('get-screen-info');
            const sources = [];
            
            for (let i = 0; i < screenInfo.displays.length; i++) {
                const display = screenInfo.displays[i];
                sources.push({
                    id: `display:${i}`,
                    name: `Display ${i + 1}`,
                    thumbnail: await this.createDisplayThumbnail(display),
                    display: display
                });
            }
            
            return sources;
        } catch (error) {
            console.error('Canvas-based source detection failed:', error);
            return [];
        }
    }

    async createThumbnailFromStream(stream) {
        return new Promise((resolve) => {
            const video = document.createElement('video');
            video.srcObject = stream;
            video.autoplay = true;
            video.muted = true;
            
            video.onloadedmetadata = () => {
                const canvas = document.createElement('canvas');
                canvas.width = 150;
                canvas.height = 150;
                const ctx = canvas.getContext('2d');
                
                video.currentTime = 0;
                video.onseeked = () => {
                    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
                    const thumbnail = canvas.toDataURL();
                    stream.getTracks().forEach(track => track.stop());
                    resolve(thumbnail);
                };
            };
        });
    }

    async createDisplayThumbnail(display) {
        // Create a simple thumbnail representation
        const canvas = document.createElement('canvas');
        canvas.width = 150;
        canvas.height = 150;
        const ctx = canvas.getContext('2d');
        
        // Draw a simple representation
        ctx.fillStyle = '#1e1e1e';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.fillStyle = '#00a8ff';
        ctx.fillRect(10, 10, canvas.width - 20, canvas.height - 20);
        ctx.fillStyle = '#ffffff';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(`${display.bounds.width}x${display.bounds.height}`, canvas.width / 2, canvas.height / 2);
        
        return canvas.toDataURL();
    }

    async startCapture(sourceId, options = {}) {
        try {
            this.quality = options.quality || 'medium';
            this.frameRate = options.frameRate || 30;
            
            if (this.sebCompatible) {
                return await this.startSEBCompatibleCapture(sourceId, options);
            } else {
                return await this.startStandardCapture(sourceId, options);
            }
        } catch (error) {
            console.error('Error starting capture:', error);
            throw error;
        }
    }

    async startStandardCapture(sourceId, options) {
        const constraints = {
            audio: options.audio !== false ? {
                mandatory: {
                    chromeMediaSource: 'desktop'
                }
            } : false,
            video: {
                mandatory: {
                    chromeMediaSource: 'desktop',
                    chromeMediaSourceId: sourceId,
                    maxWidth: this.getQualitySettings().width,
                    maxHeight: this.getQualitySettings().height,
                    maxFrameRate: this.frameRate
                }
            }
        };

        this.captureStream = await navigator.mediaDevices.getUserMedia(constraints);
        this.isCapturing = true;
        
        return this.captureStream;
    }

    async startSEBCompatibleCapture(sourceId, options) {
        try {
            // Method 1: Try getDisplayMedia
            if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
                this.captureStream = await navigator.mediaDevices.getDisplayMedia({
                    video: {
                        width: { ideal: this.getQualitySettings().width },
                        height: { ideal: this.getQualitySettings().height },
                        frameRate: { ideal: this.frameRate }
                    },
                    audio: options.audio !== false
                });
                
                this.isCapturing = true;
                return this.captureStream;
            }
            
            // Method 2: Canvas-based capture
            return await this.startCanvasCapture(sourceId, options);
            
        } catch (error) {
            console.error('SEB compatible capture failed:', error);
            throw error;
        }
    }

    async startCanvasCapture(sourceId, options) {
        // Create a canvas-based capture system
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const qualitySettings = this.getQualitySettings();
        
        canvas.width = qualitySettings.width;
        canvas.height = qualitySettings.height;
        
        // Create a MediaStream from canvas
        this.captureStream = canvas.captureStream(this.frameRate);
        this.canvas = canvas;
        this.ctx = ctx;
        
        // Start capturing screen content
        this.startCanvasScreenCapture();
        
        this.isCapturing = true;
        return this.captureStream;
    }

    startCanvasScreenCapture() {
        // This is a simplified version - in a real implementation,
        // you would need to use native screen capture APIs
        const captureFrame = () => {
            if (!this.isCapturing) return;
            
            // Simulate screen capture by drawing current window content
            // In a real implementation, this would capture actual screen content
            this.drawSimulatedScreen();
            
            setTimeout(captureFrame, 1000 / this.frameRate);
        };
        
        captureFrame();
    }

    drawSimulatedScreen() {
        if (!this.ctx) return;
        
        // Clear canvas
        this.ctx.fillStyle = '#1e1e1e';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Draw simulated content
        this.ctx.fillStyle = '#00a8ff';
        this.ctx.fillRect(50, 50, this.canvas.width - 100, this.canvas.height - 100);
        
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = '24px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('Screen Capture Active', this.canvas.width / 2, this.canvas.height / 2);
        
        // Add timestamp
        this.ctx.font = '12px Arial';
        this.ctx.fillText(new Date().toLocaleTimeString(), this.canvas.width / 2, this.canvas.height / 2 + 30);
    }

    getQualitySettings() {
        const settings = {
            high: { width: 1920, height: 1080 },
            medium: { width: 1280, height: 720 },
            low: { width: 854, height: 480 }
        };
        
        return settings[this.quality] || settings.medium;
    }

    stopCapture() {
        if (this.captureStream) {
            this.captureStream.getTracks().forEach(track => track.stop());
            this.captureStream = null;
        }
        
        this.isCapturing = false;
        this.canvas = null;
        this.ctx = null;
    }

    setQuality(quality) {
        this.quality = quality;
    }

    setFrameRate(frameRate) {
        this.frameRate = frameRate;
    }

    getStats() {
        if (!this.captureStream) return null;
        
        const videoTrack = this.captureStream.getVideoTracks()[0];
        if (!videoTrack) return null;
        
        return videoTrack.getSettings();
    }

    async takeScreenshot() {
        if (!this.captureStream) return null;
        
        const video = document.createElement('video');
        video.srcObject = this.captureStream;
        video.autoplay = true;
        video.muted = true;
        
        return new Promise((resolve) => {
            video.onloadedmetadata = () => {
                const canvas = document.createElement('canvas');
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                const ctx = canvas.getContext('2d');
                
                ctx.drawImage(video, 0, 0);
                const screenshot = canvas.toDataURL('image/png');
                resolve(screenshot);
            };
        });
    }
}

module.exports = ScreenCapture;
