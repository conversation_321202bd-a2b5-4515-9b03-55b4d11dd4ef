const { Server } = require('socket.io');
const SignalingServer = require('../../src/server/signaling-server');

exports.handler = async (event, context) => {
  // Serverless function for signaling
  const server = new SignalingServer();
  
  // Handle WebSocket connections
  if (event.httpMethod === 'GET' && event.path === '/health') {
    return {
      statusCode: 200,
      body: JSON.stringify({ status: 'healthy', timestamp: new Date().toISOString() })
    };
  }
  
  return {
    statusCode: 404,
    body: JSON.stringify({ error: 'Not found' })
  };
};