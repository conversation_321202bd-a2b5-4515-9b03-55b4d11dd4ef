/**
 * Safe Exam <PERSON>er (SEB) Detection Utility
 * Detects if the application is running within SEB environment
 * and provides appropriate fallback mechanisms
 */

class SEBDetector {
    constructor() {
        this.isSEB = false;
        this.sebVersion = null;
        this.restrictions = {
            screenCapture: false,
            clipboard: false,
            fileAccess: false,
            networkAccess: true
        };

        this.detect();
    }

    detect() {
        // Method 1: User Agent Detection
        const userAgent = navigator.userAgent;
        if (userAgent.includes('SEB') ||
            userAgent.includes('SafeExamBrowser') ||
            userAgent.includes('seb://')) {
            this.isSEB = true;
            this.extractSEBVersion(userAgent);
        }

        // Method 2: Protocol Detection
        if (window.location.protocol === 'seb:') {
            this.isSEB = true;
        }

        // Method 3: Window Properties Detection
        if (window.sebInfo || window.SEB || window.SafeExamBrowser) {
            this.isSEB = true;
        }

        // Method 4: Kiosk Mode Detection
        if (navigator.userAgent.includes('kiosk') ||
            window.screen.availHeight === window.screen.height) {
            this.isSEB = true;
        }

        // Method 5: Restricted Environment Detection
        if (this.detectRestrictedEnvironment()) {
            this.isSEB = true;
        }

        // Method 6: API Restrictions Detection
        this.detectAPIRestrictions();

        // Method 7: Network Restrictions Detection
        this.detectNetworkRestrictions();

        console.log('SEB Detection Result:', {
            isSEB: this.isSEB,
            version: this.sebVersion,
            restrictions: this.restrictions,
            userAgent: userAgent
        });
    }

    extractSEBVersion(userAgent) {
        const versionMatch = userAgent.match(/SEB[\/\s](\d+\.\d+)/i);
        if (versionMatch) {
            this.sebVersion = versionMatch[1];
        }
    }

    detectAPIRestrictions() {
        // Test screen capture APIs
        try {
            if (!navigator.mediaDevices || !navigator.mediaDevices.getDisplayMedia) {
                this.restrictions.screenCapture = true;
            }
        } catch (error) {
            this.restrictions.screenCapture = true;
        }

        // Test clipboard APIs
        try {
            if (!navigator.clipboard) {
                this.restrictions.clipboard = true;
            }
        } catch (error) {
            this.restrictions.clipboard = true;
        }

        // Test file access APIs
        try {
            if (!window.File || !window.FileReader) {
                this.restrictions.fileAccess = true;
            }
        } catch (error) {
            this.restrictions.fileAccess = true;
        }
    }

    detectRestrictedEnvironment() {
        // Check for common SEB restrictions
        const restrictions = [
            // Check if right-click is disabled
            () => {
                try {
                    document.addEventListener('contextmenu', (e) => e.preventDefault());
                    return true;
                } catch (error) {
                    return false;
                }
            },

            // Check if certain keyboard shortcuts are disabled
            () => {
                try {
                    return !window.navigator.keyboard;
                } catch (error) {
                    return true;
                }
            },

            // Check if developer tools are restricted
            () => {
                try {
                    return typeof window.chrome === 'undefined' &&
                           typeof window.safari === 'undefined';
                } catch (error) {
                    return true;
                }
            }
        ];

        return restrictions.some(check => check());
    }

    detectNetworkRestrictions() {
        // Test network connectivity patterns typical of SEB
        try {
            // Check if only specific protocols are allowed
            const allowedProtocols = ['http:', 'https:', 'seb:'];
            if (!allowedProtocols.includes(window.location.protocol)) {
                this.restrictions.networkAccess = false;
            }

            // Check for proxy or filtering
            if (navigator.connection && navigator.connection.type === 'unknown') {
                this.isSEB = true;
            }
        } catch (error) {
            // Network detection failed, assume restricted
            this.restrictions.networkAccess = false;
        }
    }

    // Public methods
    isSEBEnvironment() {
        return this.isSEB;
    }

    getSEBVersion() {
        return this.sebVersion;
    }

    getRestrictions() {
        return { ...this.restrictions };
    }

    isAPIRestricted(apiName) {
        return this.restrictions[apiName] || false;
    }

    // SEB-specific configurations
    getSEBCompatibleConfig() {
        if (!this.isSEB) {
            return null;
        }

        return {
            // WebRTC configuration for SEB
            webrtc: {
                iceServers: [
                    { urls: 'stun:stun.l.google.com:19302' },
                    { urls: 'stun:stun1.l.google.com:19302' }
                ],
                iceCandidatePoolSize: 5,
                bundlePolicy: 'balanced',
                rtcpMuxPolicy: 'require'
            },

            // Screen capture settings
            capture: {
                method: 'canvas', // Use canvas instead of native APIs
                fallback: true,
                quality: 'medium',
                frameRate: 15 // Lower frame rate for better performance
            },

            // Security settings
            security: {
                allowInsecureConnections: true,
                disableWebSecurity: true,
                allowRunningInsecureContent: true
            },

            // UI adjustments
            ui: {
                showSEBWarning: true,
                disableDevTools: true,
                hideMenuBar: true
            }
        };
    }

    // Fallback methods for restricted APIs
    async getScreenCaptureFallback() {
        if (!this.isSEB) {
            return null;
        }

        // Method 1: Try getDisplayMedia with reduced constraints
        try {
            const stream = await navigator.mediaDevices.getDisplayMedia({
                video: {
                    width: { ideal: 1280, max: 1280 },
                    height: { ideal: 720, max: 720 },
                    frameRate: { ideal: 15, max: 30 }
                },
                audio: false
            });
            return stream;
        } catch (error) {
            console.log('getDisplayMedia failed, trying canvas method');
        }

        // Method 2: Canvas-based capture
        return this.createCanvasCapture();
    }

    createCanvasCapture() {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        canvas.width = 1280;
        canvas.height = 720;

        // Create a MediaStream from canvas
        const stream = canvas.captureStream(15);

        // Simulate screen content
        const drawFrame = () => {
            ctx.fillStyle = '#1e1e1e';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            ctx.fillStyle = '#00a8ff';
            ctx.fillRect(50, 50, canvas.width - 100, canvas.height - 100);

            ctx.fillStyle = '#ffffff';
            ctx.font = '24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('SEB Screen Capture', canvas.width / 2, canvas.height / 2);
            ctx.fillText(new Date().toLocaleTimeString(), canvas.width / 2, canvas.height / 2 + 40);

            requestAnimationFrame(drawFrame);
        };

        drawFrame();
        return stream;
    }

    // Clipboard fallback
    async getClipboardFallback() {
        if (!this.restrictions.clipboard) {
            return navigator.clipboard;
        }

        // Create a fallback clipboard implementation
        return {
            writeText: async (text) => {
                // Store in localStorage as fallback
                localStorage.setItem('clipboard_text', text);
                return Promise.resolve();
            },
            readText: async () => {
                // Read from localStorage
                return Promise.resolve(localStorage.getItem('clipboard_text') || '');
            }
        };
    }

    // Network configuration for SEB
    getNetworkConfig() {
        if (!this.isSEB) {
            return {};
        }

        return {
            // Use HTTP instead of HTTPS if needed
            protocol: 'http',

            // Alternative signaling server ports
            signalingPorts: [3001, 8080, 80],

            // STUN/TURN servers that work in restricted environments
            iceServers: [
                { urls: 'stun:stun.l.google.com:19302' },
                { urls: 'stun:stun1.l.google.com:19302' },
                { urls: 'stun:stun.services.mozilla.com' }
            ]
        };
    }

    // Performance optimizations for SEB
    getPerformanceConfig() {
        if (!this.isSEB) {
            return {};
        }

        return {
            // Reduced quality settings
            video: {
                width: 1280,
                height: 720,
                frameRate: 15,
                bitrate: 1000000 // 1 Mbps
            },

            // Optimized rendering
            rendering: {
                hardwareAcceleration: false,
                vsync: false,
                maxFPS: 15
            },

            // Memory management
            memory: {
                maxCacheSize: 50 * 1024 * 1024, // 50MB
                gcInterval: 30000 // 30 seconds
            }
        };
    }

    // Warning messages for users
    getSEBWarnings() {
        if (!this.isSEB) {
            return [];
        }

        const warnings = [];

        if (this.restrictions.screenCapture) {
            warnings.push('Screen capture may be limited in this environment. Using fallback method.');
        }

        if (this.restrictions.clipboard) {
            warnings.push('Clipboard synchronization may not work properly.');
        }

        if (this.restrictions.fileAccess) {
            warnings.push('File transfer features are disabled.');
        }

        return warnings;
    }
}

// Singleton instance
const sebDetector = new SEBDetector();

module.exports = sebDetector;
