version: '3.8'

services:
  remote-desktop-server:
    build: .
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - HOST=0.0.0.0
      - PORT=3001
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3001/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) }).on('error', () => process.exit(1))"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - remote-desktop-network

networks:
  remote-desktop-network:
    driver: bridge
