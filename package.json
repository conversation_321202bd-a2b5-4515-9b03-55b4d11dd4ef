{"name": "remote-desktop-app", "version": "1.0.0", "description": "Remote Desktop Application compatible with SEB (Safe Exam Browser)", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "server": "node src/server/signaling-server.js", "test": "jest"}, "keywords": ["remote-desktop", "electron", "webrtc", "screen-sharing", "seb-compatible"], "author": "Your Name", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.0.0", "jest": "^29.0.0"}, "dependencies": {"socket.io": "^4.7.0", "socket.io-client": "^4.7.0", "express": "^4.18.0", "cors": "^2.8.5", "node-screenshots": "^0.1.0", "robotjs": "^0.6.0"}, "build": {"appId": "com.yourcompany.remote-desktop", "productName": "Remote Desktop App", "directories": {"output": "dist"}, "files": ["main.js", "src/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}, "electronmon": {"patterns": ["!**/**", "src/**"]}}