{"name": "Remote Desktop Signaling Server", "description": "WebRTC signaling server for Remote Desktop App with SEB compatibility", "repository": "https://github.com/your-username/remote-desktop-app", "logo": "https://cdn.jsdelivr.net/npm/simple-icons@v3/icons/electron.svg", "keywords": ["webrtc", "remote-desktop", "signaling-server", "seb", "electron"], "image": "hero<PERSON>/nodejs", "stack": "heroku-22", "buildpacks": [{"url": "hero<PERSON>/nodejs"}], "formation": {"web": {"quantity": 1, "size": "basic"}}, "addons": [], "env": {"NODE_ENV": {"description": "Node environment", "value": "production"}, "HOST": {"description": "Server host", "value": "0.0.0.0"}}, "scripts": {"postdeploy": "echo 'Remote Desktop Signaling Server deployed successfully!'"}}