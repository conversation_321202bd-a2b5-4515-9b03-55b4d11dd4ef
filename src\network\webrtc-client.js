class WebRTCClient {
    constructor(signalingSocket) {
        this.socket = signalingSocket;
        this.peerConnection = null;
        this.localStream = null;
        this.remoteStream = null;
        this.dataChannel = null;
        this.isInitiator = false;
        this.remoteId = null;
        this.connectionState = 'disconnected';
        this.stats = {
            bytesReceived: 0,
            bytesSent: 0,
            packetsLost: 0,
            latency: 0,
            fps: 0
        };
        
        this.configuration = {
            iceServers: [
                { urls: 'stun:stun.l.google.com:19302' },
                { urls: 'stun:stun1.l.google.com:19302' },
                { urls: 'stun:stun2.l.google.com:19302' },
                { urls: 'stun:stun3.l.google.com:19302' },
                { urls: 'stun:stun4.l.google.com:19302' }
            ],
            iceCandidatePoolSize: 10
        };
        
        this.setupSocketHandlers();
        this.initializePeerConnection();
    }

    setupSocketHandlers() {
        this.socket.on('offer', async (data) => {
            await this.handleOffer(data);
        });

        this.socket.on('answer', async (data) => {
            await this.handleAnswer(data);
        });

        this.socket.on('ice-candidate', async (data) => {
            await this.handleIceCandidate(data);
        });

        this.socket.on('user-disconnected', (userId) => {
            if (userId === this.remoteId) {
                this.handleRemoteDisconnect();
            }
        });
    }

    initializePeerConnection() {
        this.peerConnection = new RTCPeerConnection(this.configuration);

        // Handle ICE candidates
        this.peerConnection.onicecandidate = (event) => {
            if (event.candidate && this.remoteId) {
                this.socket.emit('ice-candidate', {
                    to: this.remoteId,
                    candidate: event.candidate
                });
            }
        };

        // Handle remote stream
        this.peerConnection.ontrack = (event) => {
            console.log('Received remote stream');
            this.remoteStream = event.streams[0];
            this.onRemoteStream(this.remoteStream);
        };

        // Handle data channel
        this.peerConnection.ondatachannel = (event) => {
            const channel = event.channel;
            this.setupDataChannel(channel);
        };

        // Handle connection state changes
        this.peerConnection.onconnectionstatechange = () => {
            this.connectionState = this.peerConnection.connectionState;
            console.log('Connection state:', this.connectionState);
            this.onConnectionStateChange(this.connectionState);
            
            if (this.connectionState === 'connected') {
                this.startStatsCollection();
            } else if (this.connectionState === 'disconnected' || this.connectionState === 'failed') {
                this.stopStatsCollection();
            }
        };

        // Handle ICE connection state changes
        this.peerConnection.oniceconnectionstatechange = () => {
            console.log('ICE connection state:', this.peerConnection.iceConnectionState);
        };

        // Handle ICE gathering state changes
        this.peerConnection.onicegatheringstatechange = () => {
            console.log('ICE gathering state:', this.peerConnection.iceGatheringState);
        };
    }

    async createOffer(remoteId) {
        try {
            this.remoteId = remoteId;
            this.isInitiator = true;

            // Create data channel for control messages
            this.dataChannel = this.peerConnection.createDataChannel('control', {
                ordered: true,
                maxRetransmits: 3
            });
            this.setupDataChannel(this.dataChannel);

            // Create offer
            const offer = await this.peerConnection.createOffer({
                offerToReceiveAudio: true,
                offerToReceiveVideo: true
            });

            await this.peerConnection.setLocalDescription(offer);

            // Send offer through signaling server
            this.socket.emit('offer', {
                to: remoteId,
                offer: offer
            });

            console.log('Offer sent to:', remoteId);
            return true;

        } catch (error) {
            console.error('Error creating offer:', error);
            throw error;
        }
    }

    async handleOffer(data) {
        try {
            this.remoteId = data.from;
            this.isInitiator = false;

            await this.peerConnection.setRemoteDescription(data.offer);

            // Create answer
            const answer = await this.peerConnection.createAnswer();
            await this.peerConnection.setLocalDescription(answer);

            // Send answer through signaling server
            this.socket.emit('answer', {
                to: data.from,
                answer: answer
            });

            console.log('Answer sent to:', data.from);

        } catch (error) {
            console.error('Error handling offer:', error);
            throw error;
        }
    }

    async handleAnswer(data) {
        try {
            await this.peerConnection.setRemoteDescription(data.answer);
            console.log('Answer received from:', data.from);

        } catch (error) {
            console.error('Error handling answer:', error);
            throw error;
        }
    }

    async handleIceCandidate(data) {
        try {
            if (data.candidate) {
                await this.peerConnection.addIceCandidate(data.candidate);
            }
        } catch (error) {
            console.error('Error handling ICE candidate:', error);
        }
    }

    setupDataChannel(channel) {
        this.dataChannel = channel;

        channel.onopen = () => {
            console.log('Data channel opened');
            this.onDataChannelOpen();
        };

        channel.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.onDataChannelMessage(data);
            } catch (error) {
                console.error('Error parsing data channel message:', error);
            }
        };

        channel.onclose = () => {
            console.log('Data channel closed');
            this.onDataChannelClose();
        };

        channel.onerror = (error) => {
            console.error('Data channel error:', error);
        };
    }

    addLocalStream(stream) {
        this.localStream = stream;
        
        // Add tracks to peer connection
        stream.getTracks().forEach(track => {
            this.peerConnection.addTrack(track, stream);
        });

        console.log('Local stream added');
    }

    removeLocalStream() {
        if (this.localStream) {
            this.localStream.getTracks().forEach(track => {
                track.stop();
            });
            this.localStream = null;
        }

        // Remove tracks from peer connection
        const senders = this.peerConnection.getSenders();
        senders.forEach(sender => {
            if (sender.track) {
                this.peerConnection.removeTrack(sender);
            }
        });

        console.log('Local stream removed');
    }

    sendDataChannelMessage(message) {
        if (this.dataChannel && this.dataChannel.readyState === 'open') {
            try {
                this.dataChannel.send(JSON.stringify(message));
                return true;
            } catch (error) {
                console.error('Error sending data channel message:', error);
                return false;
            }
        }
        return false;
    }

    async getConnectionStats() {
        if (!this.peerConnection) return null;

        try {
            const stats = await this.peerConnection.getStats();
            const report = {};

            stats.forEach((stat) => {
                if (stat.type === 'inbound-rtp' && stat.mediaType === 'video') {
                    report.inboundVideo = {
                        bytesReceived: stat.bytesReceived,
                        packetsReceived: stat.packetsReceived,
                        packetsLost: stat.packetsLost,
                        framesDecoded: stat.framesDecoded,
                        frameWidth: stat.frameWidth,
                        frameHeight: stat.frameHeight
                    };
                } else if (stat.type === 'outbound-rtp' && stat.mediaType === 'video') {
                    report.outboundVideo = {
                        bytesSent: stat.bytesSent,
                        packetsSent: stat.packetsSent,
                        framesEncoded: stat.framesEncoded,
                        frameWidth: stat.frameWidth,
                        frameHeight: stat.frameHeight
                    };
                } else if (stat.type === 'candidate-pair' && stat.state === 'succeeded') {
                    report.connection = {
                        currentRoundTripTime: stat.currentRoundTripTime,
                        availableOutgoingBitrate: stat.availableOutgoingBitrate,
                        availableIncomingBitrate: stat.availableIncomingBitrate
                    };
                }
            });

            return report;

        } catch (error) {
            console.error('Error getting connection stats:', error);
            return null;
        }
    }

    startStatsCollection() {
        this.statsInterval = setInterval(async () => {
            const stats = await this.getConnectionStats();
            if (stats) {
                this.updateStats(stats);
                this.onStatsUpdate(this.stats);
            }
        }, 1000);
    }

    stopStatsCollection() {
        if (this.statsInterval) {
            clearInterval(this.statsInterval);
            this.statsInterval = null;
        }
    }

    updateStats(report) {
        if (report.inboundVideo) {
            this.stats.bytesReceived = report.inboundVideo.bytesReceived || 0;
            this.stats.packetsLost = report.inboundVideo.packetsLost || 0;
            this.stats.fps = report.inboundVideo.framesDecoded || 0;
        }

        if (report.outboundVideo) {
            this.stats.bytesSent = report.outboundVideo.bytesSent || 0;
        }

        if (report.connection) {
            this.stats.latency = Math.round((report.connection.currentRoundTripTime || 0) * 1000);
        }
    }

    disconnect() {
        this.stopStatsCollection();

        if (this.dataChannel) {
            this.dataChannel.close();
            this.dataChannel = null;
        }

        if (this.localStream) {
            this.removeLocalStream();
        }

        if (this.peerConnection) {
            this.peerConnection.close();
            this.peerConnection = null;
        }

        this.connectionState = 'disconnected';
        this.remoteId = null;
        this.isInitiator = false;

        // Reinitialize for next connection
        this.initializePeerConnection();

        console.log('WebRTC client disconnected');
    }

    handleRemoteDisconnect() {
        this.disconnect();
        this.onRemoteDisconnect();
    }

    // Event handlers (to be overridden)
    onRemoteStream(stream) {
        console.log('Remote stream received');
    }

    onConnectionStateChange(state) {
        console.log('Connection state changed:', state);
    }

    onDataChannelOpen() {
        console.log('Data channel opened');
    }

    onDataChannelMessage(data) {
        console.log('Data channel message:', data);
    }

    onDataChannelClose() {
        console.log('Data channel closed');
    }

    onStatsUpdate(stats) {
        console.log('Stats update:', stats);
    }

    onRemoteDisconnect() {
        console.log('Remote peer disconnected');
    }

    // Getters
    getConnectionState() {
        return this.connectionState;
    }

    getRemoteId() {
        return this.remoteId;
    }

    getStats() {
        return { ...this.stats };
    }

    isConnected() {
        return this.connectionState === 'connected';
    }

    hasDataChannel() {
        return this.dataChannel && this.dataChannel.readyState === 'open';
    }
}

module.exports = WebRTCClient;
