// Heroku deployment script
const { execSync } = require('child_process');
const fs = require('fs');

console.log('🚀 Deploying Remote Desktop Server to Heroku');
console.log('============================================');

const config = {
    appName: 'remote-desktop-seb',
    region: 'us',
    stack: 'heroku-22'
};

// Check if Heroku CLI is installed
function checkHerokuCLI() {
    console.log('🔍 Checking Heroku CLI...');
    try {
        execSync('heroku --version', { stdio: 'pipe' });
        console.log('✅ Heroku CLI is installed');
        return true;
    } catch (error) {
        console.log('❌ Heroku CLI not found');
        console.log('📥 Please install Heroku CLI: https://devcenter.heroku.com/articles/heroku-cli');
        return false;
    }
}

// Login to Heroku
function loginToHeroku() {
    console.log('🔐 Checking Heroku authentication...');
    try {
        execSync('heroku auth:whoami', { stdio: 'pipe' });
        console.log('✅ Already logged in to Heroku');
        return true;
    } catch (error) {
        console.log('🔐 Please login to Heroku...');
        try {
            execSync('heroku login', { stdio: 'inherit' });
            return true;
        } catch (loginError) {
            console.log('❌ Failed to login to Heroku');
            return false;
        }
    }
}

// Create Heroku app
function createHerokuApp() {
    console.log(`🏗️  Creating Heroku app: ${config.appName}...`);
    try {
        // Check if app already exists
        try {
            execSync(`heroku apps:info ${config.appName}`, { stdio: 'pipe' });
            console.log('✅ Heroku app already exists');
            return true;
        } catch (error) {
            // App doesn't exist, create it
            execSync(`heroku create ${config.appName} --region ${config.region} --stack ${config.stack}`, { stdio: 'inherit' });
            console.log('✅ Heroku app created successfully');
            return true;
        }
    } catch (error) {
        console.log('❌ Failed to create Heroku app:', error.message);
        return false;
    }
}

// Set environment variables
function setEnvironmentVariables() {
    console.log('⚙️  Setting environment variables...');
    try {
        const envVars = [
            'NODE_ENV=production',
            'HOST=0.0.0.0'
        ];
        
        envVars.forEach(envVar => {
            execSync(`heroku config:set ${envVar} --app ${config.appName}`, { stdio: 'inherit' });
        });
        
        console.log('✅ Environment variables set');
        return true;
    } catch (error) {
        console.log('❌ Failed to set environment variables:', error.message);
        return false;
    }
}

// Prepare deployment files
function prepareDeployment() {
    console.log('📦 Preparing deployment files...');
    try {
        // Create .gitignore for deployment
        const gitignore = `node_modules/
npm-debug.log*
.env
.DS_Store
dist/
*.log`;
        
        if (!fs.existsSync('.gitignore')) {
            fs.writeFileSync('.gitignore', gitignore);
        }
        
        // Initialize git if not already
        try {
            execSync('git status', { stdio: 'pipe' });
        } catch (error) {
            execSync('git init', { stdio: 'inherit' });
            execSync('git add .', { stdio: 'inherit' });
            execSync('git commit -m "Initial commit"', { stdio: 'inherit' });
        }
        
        console.log('✅ Deployment files prepared');
        return true;
    } catch (error) {
        console.log('❌ Failed to prepare deployment:', error.message);
        return false;
    }
}

// Deploy to Heroku
function deployToHeroku() {
    console.log('🚀 Deploying to Heroku...');
    try {
        // Add Heroku remote if not exists
        try {
            execSync('git remote get-url heroku', { stdio: 'pipe' });
        } catch (error) {
            execSync(`heroku git:remote -a ${config.appName}`, { stdio: 'inherit' });
        }
        
        // Commit any changes
        try {
            execSync('git add .', { stdio: 'inherit' });
            execSync('git commit -m "Deploy to Heroku"', { stdio: 'inherit' });
        } catch (error) {
            // No changes to commit
        }
        
        // Push to Heroku
        execSync('git push heroku main', { stdio: 'inherit' });
        
        console.log('✅ Deployment completed successfully');
        return true;
    } catch (error) {
        console.log('❌ Deployment failed:', error.message);
        return false;
    }
}

// Test deployment
function testDeployment() {
    console.log('🧪 Testing deployment...');
    try {
        const appUrl = `https://${config.appName}.herokuapp.com`;
        console.log(`🌐 App URL: ${appUrl}`);
        
        // Open app in browser
        execSync(`heroku open --app ${config.appName}`, { stdio: 'inherit' });
        
        // Show logs
        console.log('📋 Recent logs:');
        execSync(`heroku logs --tail --app ${config.appName}`, { stdio: 'inherit' });
        
        return true;
    } catch (error) {
        console.log('❌ Testing failed:', error.message);
        return false;
    }
}

// Main deployment function
async function deploy() {
    console.log('🚀 Starting Heroku deployment process...\n');
    
    const steps = [
        { name: 'Check Heroku CLI', fn: checkHerokuCLI },
        { name: 'Login to Heroku', fn: loginToHeroku },
        { name: 'Create Heroku App', fn: createHerokuApp },
        { name: 'Set Environment Variables', fn: setEnvironmentVariables },
        { name: 'Prepare Deployment', fn: prepareDeployment },
        { name: 'Deploy to Heroku', fn: deployToHeroku }
    ];
    
    for (const step of steps) {
        console.log(`\n📋 ${step.name}...`);
        const success = step.fn();
        if (!success) {
            console.log(`❌ Failed at step: ${step.name}`);
            process.exit(1);
        }
    }
    
    console.log('\n🎉 Deployment completed successfully!');
    console.log('\n📋 Next steps:');
    console.log(`1. Your server is now running at: https://${config.appName}.herokuapp.com`);
    console.log('2. Update the client app to use this server URL');
    console.log('3. Build and distribute the client application');
    console.log('4. Test the complete system');
    
    console.log('\n🔧 Useful commands:');
    console.log(`heroku logs --tail --app ${config.appName}  # View logs`);
    console.log(`heroku restart --app ${config.appName}      # Restart app`);
    console.log(`heroku open --app ${config.appName}         # Open in browser`);
}

// Run deployment if script is executed directly
if (require.main === module) {
    deploy().catch(error => {
        console.error('❌ Deployment failed:', error);
        process.exit(1);
    });
}

module.exports = { deploy, config };
