// SEB Compatibility Test Suite
const { execSync } = require('child_process');
const http = require('http');
const fs = require('fs');

console.log('🧪 SEB Compatibility Test Suite');
console.log('===============================');

class SEBCompatibilityTester {
    constructor() {
        this.testResults = [];
        this.serverUrl = 'http://localhost:3001';
        this.publicServerUrl = 'https://remote-desktop-seb.herokuapp.com';
    }

    // Test SEB detection logic
    async testSEBDetection() {
        console.log('\n🔍 Testing SEB Detection...');
        
        const tests = [
            {
                name: 'User Agent Detection',
                test: () => {
                    // Simulate SEB user agent
                    const sebUserAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) SEB/3.0.0 Chrome/91.0.4472.124 Safari/537.36';
                    return sebUserAgent.includes('SEB');
                }
            },
            {
                name: 'Protocol Detection',
                test: () => {
                    // Test seb:// protocol detection
                    return 'seb:'.startsWith('seb:');
                }
            },
            {
                name: 'API Restrictions',
                test: () => {
                    // Test for restricted APIs
                    try {
                        return typeof navigator !== 'undefined';
                    } catch (error) {
                        return false;
                    }
                }
            }
        ];

        for (const test of tests) {
            try {
                const result = test.test();
                console.log(`${result ? '✅' : '❌'} ${test.name}: ${result ? 'PASS' : 'FAIL'}`);
                this.testResults.push({ name: test.name, passed: result });
            } catch (error) {
                console.log(`❌ ${test.name}: ERROR - ${error.message}`);
                this.testResults.push({ name: test.name, passed: false, error: error.message });
            }
        }
    }

    // Test server connectivity
    async testServerConnectivity() {
        console.log('\n🌐 Testing Server Connectivity...');
        
        const servers = [
            { name: 'Local Server', url: this.serverUrl },
            { name: 'Public Server', url: this.publicServerUrl }
        ];

        for (const server of servers) {
            try {
                const result = await this.testServerHealth(server.url);
                console.log(`${result ? '✅' : '❌'} ${server.name}: ${result ? 'ONLINE' : 'OFFLINE'}`);
                this.testResults.push({ name: `${server.name} Connectivity`, passed: result });
            } catch (error) {
                console.log(`❌ ${server.name}: ERROR - ${error.message}`);
                this.testResults.push({ name: `${server.name} Connectivity`, passed: false, error: error.message });
            }
        }
    }

    // Test server health
    testServerHealth(url) {
        return new Promise((resolve) => {
            const healthUrl = `${url}/health`;
            const req = http.get(healthUrl, (res) => {
                resolve(res.statusCode === 200);
            });
            req.on('error', () => resolve(false));
            req.setTimeout(5000, () => {
                req.destroy();
                resolve(false);
            });
        });
    }

    // Test WebRTC compatibility
    async testWebRTCCompatibility() {
        console.log('\n📡 Testing WebRTC Compatibility...');
        
        const tests = [
            {
                name: 'RTCPeerConnection Support',
                test: () => typeof RTCPeerConnection !== 'undefined'
            },
            {
                name: 'getUserMedia Support',
                test: () => navigator.mediaDevices && typeof navigator.mediaDevices.getUserMedia === 'function'
            },
            {
                name: 'getDisplayMedia Support',
                test: () => navigator.mediaDevices && typeof navigator.mediaDevices.getDisplayMedia === 'function'
            },
            {
                name: 'DataChannel Support',
                test: () => {
                    try {
                        const pc = new RTCPeerConnection();
                        const dc = pc.createDataChannel('test');
                        pc.close();
                        return true;
                    } catch (error) {
                        return false;
                    }
                }
            }
        ];

        for (const test of tests) {
            try {
                const result = test.test();
                console.log(`${result ? '✅' : '❌'} ${test.name}: ${result ? 'SUPPORTED' : 'NOT SUPPORTED'}`);
                this.testResults.push({ name: test.name, passed: result });
            } catch (error) {
                console.log(`❌ ${test.name}: ERROR - ${error.message}`);
                this.testResults.push({ name: test.name, passed: false, error: error.message });
            }
        }
    }

    // Test screen capture fallbacks
    async testScreenCaptureFallbacks() {
        console.log('\n📹 Testing Screen Capture Fallbacks...');
        
        const tests = [
            {
                name: 'Canvas API Support',
                test: () => {
                    try {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');
                        return ctx !== null;
                    } catch (error) {
                        return false;
                    }
                }
            },
            {
                name: 'Canvas Stream Support',
                test: () => {
                    try {
                        const canvas = document.createElement('canvas');
                        return typeof canvas.captureStream === 'function';
                    } catch (error) {
                        return false;
                    }
                }
            },
            {
                name: 'Video Element Support',
                test: () => {
                    try {
                        const video = document.createElement('video');
                        return video !== null;
                    } catch (error) {
                        return false;
                    }
                }
            }
        ];

        for (const test of tests) {
            try {
                const result = test.test();
                console.log(`${result ? '✅' : '❌'} ${test.name}: ${result ? 'AVAILABLE' : 'NOT AVAILABLE'}`);
                this.testResults.push({ name: test.name, passed: result });
            } catch (error) {
                console.log(`❌ ${test.name}: ERROR - ${error.message}`);
                this.testResults.push({ name: test.name, passed: false, error: error.message });
            }
        }
    }

    // Test network restrictions
    async testNetworkRestrictions() {
        console.log('\n🔒 Testing Network Restrictions...');
        
        const tests = [
            {
                name: 'HTTPS Support',
                test: () => location.protocol === 'https:' || location.protocol === 'file:'
            },
            {
                name: 'WebSocket Support',
                test: () => typeof WebSocket !== 'undefined'
            },
            {
                name: 'Fetch API Support',
                test: () => typeof fetch !== 'undefined'
            },
            {
                name: 'CORS Support',
                test: () => typeof XMLHttpRequest !== 'undefined'
            }
        ];

        for (const test of tests) {
            try {
                const result = test.test();
                console.log(`${result ? '✅' : '❌'} ${test.name}: ${result ? 'AVAILABLE' : 'RESTRICTED'}`);
                this.testResults.push({ name: test.name, passed: result });
            } catch (error) {
                console.log(`❌ ${test.name}: ERROR - ${error.message}`);
                this.testResults.push({ name: test.name, passed: false, error: error.message });
            }
        }
    }

    // Test performance benchmarks
    async testPerformanceBenchmarks() {
        console.log('\n⚡ Testing Performance Benchmarks...');
        
        const tests = [
            {
                name: 'Memory Usage',
                test: () => {
                    if (performance.memory) {
                        const memoryMB = performance.memory.usedJSHeapSize / 1024 / 1024;
                        return memoryMB < 200; // Less than 200MB
                    }
                    return true; // Assume OK if not available
                }
            },
            {
                name: 'Canvas Performance',
                test: () => {
                    const start = performance.now();
                    const canvas = document.createElement('canvas');
                    canvas.width = 1280;
                    canvas.height = 720;
                    const ctx = canvas.getContext('2d');
                    ctx.fillRect(0, 0, canvas.width, canvas.height);
                    const end = performance.now();
                    return (end - start) < 100; // Less than 100ms
                }
            },
            {
                name: 'Network Latency',
                test: async () => {
                    const start = performance.now();
                    try {
                        await fetch('/health');
                        const end = performance.now();
                        return (end - start) < 1000; // Less than 1 second
                    } catch (error) {
                        return false;
                    }
                }
            }
        ];

        for (const test of tests) {
            try {
                const result = await test.test();
                console.log(`${result ? '✅' : '❌'} ${test.name}: ${result ? 'GOOD' : 'POOR'}`);
                this.testResults.push({ name: test.name, passed: result });
            } catch (error) {
                console.log(`❌ ${test.name}: ERROR - ${error.message}`);
                this.testResults.push({ name: test.name, passed: false, error: error.message });
            }
        }
    }

    // Generate test report
    generateReport() {
        console.log('\n📊 Test Report');
        console.log('==============');
        
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(test => test.passed).length;
        const failedTests = totalTests - passedTests;
        
        console.log(`\n📈 Summary:`);
        console.log(`Total Tests: ${totalTests}`);
        console.log(`Passed: ${passedTests} ✅`);
        console.log(`Failed: ${failedTests} ❌`);
        console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
        
        if (failedTests > 0) {
            console.log(`\n❌ Failed Tests:`);
            this.testResults
                .filter(test => !test.passed)
                .forEach(test => {
                    console.log(`- ${test.name}${test.error ? ': ' + test.error : ''}`);
                });
        }
        
        // SEB Compatibility Assessment
        const sebCompatibilityScore = (passedTests / totalTests) * 100;
        console.log(`\n🛡️  SEB Compatibility Assessment:`);
        
        if (sebCompatibilityScore >= 90) {
            console.log('🟢 EXCELLENT - Fully compatible with SEB');
        } else if (sebCompatibilityScore >= 75) {
            console.log('🟡 GOOD - Compatible with minor limitations');
        } else if (sebCompatibilityScore >= 50) {
            console.log('🟠 FAIR - Limited compatibility, may require adjustments');
        } else {
            console.log('🔴 POOR - Significant compatibility issues');
        }
        
        // Save report to file
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                totalTests,
                passedTests,
                failedTests,
                successRate: (passedTests / totalTests) * 100,
                sebCompatibilityScore
            },
            results: this.testResults
        };
        
        fs.writeFileSync('seb-compatibility-report.json', JSON.stringify(report, null, 2));
        console.log('\n💾 Report saved to: seb-compatibility-report.json');
    }

    // Run all tests
    async runAllTests() {
        console.log('🚀 Starting SEB Compatibility Test Suite...\n');
        
        try {
            await this.testSEBDetection();
            await this.testServerConnectivity();
            await this.testWebRTCCompatibility();
            await this.testScreenCaptureFallbacks();
            await this.testNetworkRestrictions();
            await this.testPerformanceBenchmarks();
            
            this.generateReport();
            
            console.log('\n🎉 SEB Compatibility Testing Complete!');
            
        } catch (error) {
            console.error('❌ Test suite failed:', error.message);
        }
    }
}

// Run tests if script is executed directly
if (require.main === module) {
    const tester = new SEBCompatibilityTester();
    tester.runAllTests();
}

module.exports = SEBCompatibilityTester;
