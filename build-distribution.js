// Build script for creating distribution packages
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🏗️  Building Remote Desktop App Distribution');
console.log('==========================================');

// Configuration
const config = {
    appName: 'Remote Desktop SEB',
    version: '1.0.0',
    platforms: ['win32', 'darwin', 'linux'],
    architectures: ['x64'],
    outputDir: 'dist',
    serverUrl: 'https://remote-desktop-seb.herokuapp.com'
};

// Clean previous builds
function cleanBuild() {
    console.log('🧹 Cleaning previous builds...');
    try {
        if (fs.existsSync(config.outputDir)) {
            fs.rmSync(config.outputDir, { recursive: true, force: true });
        }
        fs.mkdirSync(config.outputDir, { recursive: true });
        console.log('✅ Build directory cleaned');
    } catch (error) {
        console.error('❌ Failed to clean build directory:', error.message);
    }
}

// Update server URL in client
function updateServerUrl() {
    console.log('🔧 Updating server URL for production...');
    try {
        const appJsPath = 'src/renderer/app.js';
        let content = fs.readFileSync(appJsPath, 'utf8');
        
        // Update server URLs for production
        content = content.replace(
            /const serverUrls = \[[\s\S]*?\];/,
            `const serverUrls = [
            '${config.serverUrl}', // Production server
            'http://localhost:3001', // Local development
            'http://127.0.0.1:3001'  // Local fallback
        ];`
        );
        
        fs.writeFileSync(appJsPath, content);
        console.log('✅ Server URL updated for production');
    } catch (error) {
        console.error('❌ Failed to update server URL:', error.message);
    }
}

// Create SEB configuration file
function createSEBConfig() {
    console.log('📋 Creating SEB configuration file...');
    
    const sebConfig = {
        "allowedDisplays": 1,
        "allowQuit": false,
        "allowReload": false,
        "browserWindowAllowReload": false,
        "enableAltEsc": false,
        "enableAltF4": false,
        "enableAltTab": false,
        "enableCtrlAltDel": false,
        "enableF1": false,
        "enableF2": false,
        "enableF3": false,
        "enableF4": false,
        "enableF5": false,
        "enableF6": false,
        "enableF7": false,
        "enableF8": false,
        "enableF9": false,
        "enableF10": false,
        "enableF11": false,
        "enableF12": false,
        "enablePrintScreen": false,
        "enableRightMouse": true,
        "enableZoomPage": false,
        "enableZoomText": false,
        "exitKey1": 2,
        "exitKey2": 10,
        "exitKey3": 5,
        "hashedQuitPassword": "",
        "hideBrowserWindowToolbar": true,
        "killExplorerShell": false,
        "kiosk": true,
        "mainBrowserWindowHeight": "100%",
        "mainBrowserWindowPositioning": 1,
        "mainBrowserWindowWidth": "100%",
        "monitorProcesses": false,
        "newBrowserWindowByLinkBlockForeign": true,
        "newBrowserWindowByLinkPolicy": 2,
        "newBrowserWindowByScriptBlockForeign": true,
        "newBrowserWindowByScriptPolicy": 2,
        "restartExamPasswordProtected": false,
        "sebServicePolicy": 0,
        "showMenuBar": false,
        "showReloadButton": false,
        "showTaskBar": false,
        "startURL": "file:///[APP_PATH]/src/renderer/index.html",
        "touchOptimized": false,
        "urlFilterEnable": false,
        "urlFilterEnableContentFilter": false,
        "allowedURLs": [
            config.serverUrl,
            "http://localhost:*",
            "https://cdnjs.cloudflare.com/*"
        ]
    };
    
    try {
        fs.writeFileSync(
            path.join(config.outputDir, 'seb-config.json'),
            JSON.stringify(sebConfig, null, 2)
        );
        console.log('✅ SEB configuration file created');
    } catch (error) {
        console.error('❌ Failed to create SEB config:', error.message);
    }
}

// Build Electron app
function buildElectronApp() {
    console.log('⚡ Building Electron application...');
    
    try {
        // Install electron-builder if not present
        try {
            require('electron-builder');
        } catch (error) {
            console.log('📦 Installing electron-builder...');
            execSync('npm install electron-builder --save-dev', { stdio: 'inherit' });
        }
        
        // Build for each platform
        config.platforms.forEach(platform => {
            config.architectures.forEach(arch => {
                console.log(`🔨 Building for ${platform}-${arch}...`);
                
                const buildCommand = `npx electron-builder --${platform} --${arch} --publish=never`;
                execSync(buildCommand, { stdio: 'inherit' });
                
                console.log(`✅ Build completed for ${platform}-${arch}`);
            });
        });
        
    } catch (error) {
        console.error('❌ Electron build failed:', error.message);
    }
}

// Create portable version
function createPortableVersion() {
    console.log('📦 Creating portable version...');
    
    try {
        // Create portable directory
        const portableDir = path.join(config.outputDir, 'portable');
        fs.mkdirSync(portableDir, { recursive: true });
        
        // Copy necessary files
        const filesToCopy = [
            'main.js',
            'package.json',
            'src/',
            'config/',
            'assets/'
        ];
        
        filesToCopy.forEach(file => {
            const srcPath = path.join(__dirname, file);
            const destPath = path.join(portableDir, file);
            
            if (fs.existsSync(srcPath)) {
                if (fs.statSync(srcPath).isDirectory()) {
                    fs.cpSync(srcPath, destPath, { recursive: true });
                } else {
                    fs.copyFileSync(srcPath, destPath);
                }
            }
        });
        
        // Create run script
        const runScript = `@echo off
echo Starting Remote Desktop App...
if not exist "node_modules" (
    echo Installing dependencies...
    npm install --production
)
npm start
pause`;
        
        fs.writeFileSync(path.join(portableDir, 'run.bat'), runScript);
        
        console.log('✅ Portable version created');
    } catch (error) {
        console.error('❌ Failed to create portable version:', error.message);
    }
}

// Create documentation
function createDocumentation() {
    console.log('📚 Creating distribution documentation...');
    
    const userGuide = `# Remote Desktop App - User Guide

## Quick Start

1. **Download and Install**
   - Download the installer for your platform
   - Run the installer and follow the instructions
   - Launch "Remote Desktop SEB" from your applications

2. **Share Your Screen**
   - Click "Start Sharing" button
   - Allow screen capture permissions when prompted
   - Share the generated ID with the person who wants to connect

3. **Connect to Remote Computer**
   - Get the ID from the person sharing their screen
   - Enter the ID in the "Remote ID" field
   - Click "Connect" button

## SEB (Safe Exam Browser) Usage

This application is specifically designed to work within Safe Exam Browser environments:

1. **Automatic Detection**: The app automatically detects SEB environment
2. **Optimized Performance**: Reduced quality settings for better performance
3. **Fallback Methods**: Uses canvas capture when native APIs are blocked
4. **Network Compatibility**: Works with SEB's network restrictions

## Troubleshooting

### Connection Issues
- Ensure internet connection is stable
- Check firewall settings
- Try restarting the application

### Screen Capture Issues
- Grant screen recording permissions
- Try running as administrator (Windows)
- Check SEB configuration allows screen capture

### Performance Issues
- Lower video quality in settings
- Close unnecessary applications
- Check network bandwidth

## Support
For technical support, visit: ${config.serverUrl}
`;

    try {
        fs.writeFileSync(path.join(config.outputDir, 'USER_GUIDE.md'), userGuide);
        console.log('✅ User guide created');
    } catch (error) {
        console.error('❌ Failed to create documentation:', error.message);
    }
}

// Main build process
async function build() {
    try {
        cleanBuild();
        updateServerUrl();
        createSEBConfig();
        createDocumentation();
        createPortableVersion();
        buildElectronApp();
        
        console.log('\n🎉 Distribution build completed!');
        console.log('\n📦 Output files:');
        console.log(`- Installers: ${config.outputDir}/`);
        console.log(`- Portable: ${config.outputDir}/portable/`);
        console.log(`- SEB Config: ${config.outputDir}/seb-config.json`);
        console.log(`- User Guide: ${config.outputDir}/USER_GUIDE.md`);
        
    } catch (error) {
        console.error('❌ Build process failed:', error.message);
        process.exit(1);
    }
}

// Run build if script is executed directly
if (require.main === module) {
    build();
}

module.exports = { build, config };
