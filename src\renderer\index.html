<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Remote Desktop App</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div id="app">
        <!-- Header -->
        <header class="app-header">
            <div class="header-left">
                <div class="logo">
                    <i class="fas fa-desktop"></i>
                    <span>Remote Desktop</span>
                </div>
            </div>
            <div class="header-center">
                <div class="connection-status" id="connectionStatus">
                    <i class="fas fa-circle status-indicator"></i>
                    <span>Disconnected</span>
                </div>
            </div>
            <div class="header-right">
                <button class="header-btn" id="minimizeBtn" title="Minimize">
                    <i class="fas fa-minus"></i>
                </button>
                <button class="header-btn" id="maximizeBtn" title="Maximize">
                    <i class="fas fa-square"></i>
                </button>
                <button class="header-btn close-btn" id="closeBtn" title="Close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Sidebar -->
            <aside class="sidebar">
                <div class="sidebar-section">
                    <h3><i class="fas fa-share-alt"></i> Share Screen</h3>
                    <div class="connection-id-section">
                        <label>Your ID:</label>
                        <div class="id-display">
                            <input type="text" id="myId" readonly>
                            <button id="copyIdBtn" title="Copy ID">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    <button id="startSharingBtn" class="primary-btn">
                        <i class="fas fa-play"></i>
                        Start Sharing
                    </button>
                </div>

                <div class="sidebar-section">
                    <h3><i class="fas fa-plug"></i> Connect</h3>
                    <div class="connect-section">
                        <label>Remote ID:</label>
                        <input type="text" id="remoteId" placeholder="Enter remote ID">
                        <button id="connectBtn" class="primary-btn">
                            <i class="fas fa-link"></i>
                            Connect
                        </button>
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3><i class="fas fa-cog"></i> Settings</h3>
                    <div class="settings-section">
                        <div class="setting-item">
                            <label>
                                <input type="checkbox" id="audioEnabled" checked>
                                Enable Audio
                            </label>
                        </div>
                        <div class="setting-item">
                            <label>
                                <input type="checkbox" id="clipboardSync" checked>
                                Sync Clipboard
                            </label>
                        </div>
                        <div class="setting-item">
                            <label>Quality:</label>
                            <select id="qualitySelect">
                                <option value="high">High</option>
                                <option value="medium" selected>Medium</option>
                                <option value="low">Low</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3><i class="fas fa-info-circle"></i> Info</h3>
                    <div class="info-section">
                        <div class="info-item">
                            <span>Status:</span>
                            <span id="statusText">Ready</span>
                        </div>
                        <div class="info-item">
                            <span>Latency:</span>
                            <span id="latencyText">-</span>
                        </div>
                        <div class="info-item">
                            <span>FPS:</span>
                            <span id="fpsText">-</span>
                        </div>
                    </div>
                </div>
            </aside>

            <!-- Main View -->
            <section class="main-view">
                <div class="view-container">
                    <!-- Welcome Screen -->
                    <div id="welcomeScreen" class="welcome-screen">
                        <div class="welcome-content">
                            <i class="fas fa-desktop welcome-icon"></i>
                            <h2>Welcome to Remote Desktop</h2>
                            <p>Share your screen or connect to a remote computer</p>
                            <div class="welcome-actions">
                                <button class="welcome-btn" id="welcomeShareBtn">
                                    <i class="fas fa-share-alt"></i>
                                    Share My Screen
                                </button>
                                <button class="welcome-btn" id="welcomeConnectBtn">
                                    <i class="fas fa-plug"></i>
                                    Connect to Remote
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Remote Screen View -->
                    <div id="remoteScreen" class="remote-screen" style="display: none;">
                        <div class="screen-container">
                            <canvas id="remoteCanvas"></canvas>
                            <div class="screen-overlay">
                                <div class="overlay-controls">
                                    <button id="fullscreenBtn" title="Fullscreen">
                                        <i class="fas fa-expand"></i>
                                    </button>
                                    <button id="screenshotBtn" title="Screenshot">
                                        <i class="fas fa-camera"></i>
                                    </button>
                                    <button id="disconnectBtn" title="Disconnect">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Local Screen Preview -->
                    <div id="localPreview" class="local-preview" style="display: none;">
                        <div class="preview-container">
                            <canvas id="localCanvas"></canvas>
                            <div class="preview-overlay">
                                <div class="overlay-info">
                                    <span>Sharing your screen</span>
                                    <button id="stopSharingBtn" class="stop-btn">
                                        <i class="fas fa-stop"></i>
                                        Stop Sharing
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Loading Overlay -->
        <div id="loadingOverlay" class="loading-overlay" style="display: none;">
            <div class="loading-content">
                <div class="spinner"></div>
                <p id="loadingText">Connecting...</p>
            </div>
        </div>

        <!-- Toast Notifications -->
        <div id="toastContainer" class="toast-container"></div>
    </div>

    <!-- Scripts -->
    <script src="/socket.io/socket.io.js"></script>
    <script src="app.js"></script>
</body>
</html>
